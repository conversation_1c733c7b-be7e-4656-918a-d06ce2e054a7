---
title: "Introduction"
description: "MCP Integration for your AI Application"
icon: handshake
mode: "wide"
---

# What is <PERSON><PERSON><PERSON>?

**Klavis AI is open source MCP integration layers that let AI agents use thousands of tools reliably. You can use our API to automate workflows across multiple apps with managed authentications.** 

<img
  className="block dark:hidden"
  src="/images/get-started/introduction/bridge.png"
  alt="Klavis AI Bridge"
/>
<img
  className="hidden dark:block"
  src="/images/get-started/introduction/bridge.png"
  alt="Klavis AI Bridge"
/>

With Klavis API, you can equip your AI agent with thousands of tools, without the complexity of managing authentication, handling security, or dealing with context overload.


## Build fast

- **[Strata MCP Server](/documentation/concepts/strata)**: Optimized to handle tool overload and context window limits, ideal when connecting to multiple integrations.
- **[Individual Integration](/documentation/mcp-server/overview)**: Best suited for vertical AI agents that only need to connect to a limited set of tools or a single MCP Server.

## Get started

<CardGroup cols={2}>
  <Card title="Quickstart" icon="rocket" href="/documentation/quickstart">
    Connect any integration in minutes
  </Card>
  <Card title="Integrations" icon="server" href="/documentation/mcp-server/github">
    Explore available MCP servers
  </Card>
  <Card title="Strata" icon="layer-group" href="/documentation/concepts/strata">
    Progressive tool discovery across apps
  </Card>
  <Card title="API Reference" icon="magnifying-glass" href="/api-reference/introduction">
    REST endpoints and schemas
  </Card>
</CardGroup>
