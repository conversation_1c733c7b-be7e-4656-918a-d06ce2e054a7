---
title: 'Firecrawl Deep Research'
description: 'Connect AI agents to Firecrawl Deep Research for comprehensive web-based research and analysis'
---

<Info>
  **Prerequisites**
  Before you begin, [create an account](https://www.klavis.ai/home/<USER>
</Info>

## Getting started

Connect to Firecrawl Deep Research to perform comprehensive web-based research, analysis, and in-depth topic exploration through AI agents.

<Tabs>
  <Tab title="API">
    <Steps>
      <Step title="Install the SDKs (optional)">
        <CodeGroup>
          ```bash pip
          pip install klavis
          ```
          ```bash npm
          npm install klavis
          ```
        </CodeGroup>
      </Step>
      
      <Step title="Create a server instance">
        <CodeGroup>
          ```python Python
          from klavis import Klavis
          from klavis.types import McpServerName
          
          klavis_client = Klavis(api_key="YOUR_API_KEY")
          
          # Create a Firecrawl Deep Research MCP server instance
          firecrawl_deep_server = klavis_client.mcp_server.create_server_instance(
              server_name=McpServerName.FIRECRAWL_DEEP_RESEARCH,
              user_id="user123"
          )
          ```
          
          ```typescript TypeScript
          import { KlavisClient, Klavis } from 'klavis';
          
          const klavis = new KlavisClient({ apiKey: 'YOUR_API_KEY' });
          
          // Create a Firecrawl Deep Research MCP server instance
          const firecrawlDeepServer = await klavis.mcpServer.createServerInstance({
              serverName: Klavis.McpServerName.FirecrawlDeepResearch,
              userId: "user123"
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/create" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "serverName": "Firecrawl Deep Research",
              "userId": "user123"
            }'
          ```
        </CodeGroup>
        
        <Info>
          **Response Information**: The API returns:
          - `serverUrl`: The URL for connecting your MCP client to Firecrawl Deep Research
          - `instanceId`: Unique identifier for your server instance
          - Note: Firecrawl requires an API key which should be configured after creation
        </Info>
      </Step>
      
      <Step title="Configure authentication">
        <Info>
          You can get your Firecrawl API key from the [Firecrawl website](https://firecrawl.dev/).
        </Info>
        
        <CodeGroup>
          ```python Python
          # Set the Firecrawl API key for your instance
          response = klavis_client.mcp_server.set_instance_auth(
              instance_id=firecrawl_deep_server.instance_id,
              auth_data={
                  "token": "YOUR_FIRECRAWL_API_KEY"
              }
          )
          ```
          
          ```typescript TypeScript
          // Set the Firecrawl API key for your instance
          const response = await klavis.mcpServer.setInstanceAuth({
              instanceId: firecrawlDeepServer.instanceId,
              authData: {
                  token: "YOUR_FIRECRAWL_API_KEY"
              }
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/set-auth" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "instanceId": "YOUR_INSTANCE_ID",
              "authData": {
                "token": "YOUR_FIRECRAWL_API_KEY"
              }
            }'
          ```
        </CodeGroup>
        
        <Check>
          🎉 **Your Firecrawl Deep Research MCP Server is ready!** You can now use your MCP server URL with any MCP-compatible client to perform comprehensive research.
        </Check>
      </Step>
      

    </Steps>
  </Tab>
  
  <Tab title="UI">
    <Steps>
      <Step title="Open Dashboard">
        Go to your [Dashboard](https://www.klavis.ai/home/<USER>
      </Step>
      
      <Step title="Choose Firecrawl Deep Research">
        Select Firecrawl Deep Research from the list of available integrations.
      </Step>
      
      <Step title="Configure API Key">
        Enter your Firecrawl API key to enable research functionality.
      </Step>
      
      <Step title="Use in your app">
        Copy the MCP endpoint URL and add it to your MCP-supported client (Claude Desktop, Cursor, VS Code, etc.).
      </Step>
    </Steps>
  </Tab>
  
  <Tab title="Open Source">
    <Steps>
      <Step title="Clone repository (for developers to build from source)">
        ```bash
        git clone https://github.com/klavis-ai/klavis
        cd klavis/mcp_servers/firecrawl_deep_research
        ```
      </Step>
      
      <Step title="Run with Docker">
        ```bash
        # Pull the Docker image
        docker pull ghcr.io/klavis-ai/firecrawl-deep-research-mcp-server:latest
        
        # Run with Firecrawl API key
        docker run -p 5000:5000 \
          -e AUTH_DATA='{"token":"your_firecrawl_api_key"}' \
          ghcr.io/klavis-ai/firecrawl-deep-research-mcp-server:latest
        ```
      </Step>
      
      <Step title="Configure MCP client">
        ```json
        {
          "mcpServers": {
            "firecrawl_deep": {
              "url": "http://localhost:5000/mcp/"
            }
          }
        }
        ```
      </Step>
    </Steps>
  </Tab>
</Tabs>

## Available Tools

<Accordion title="Tools Information">
| Tool Name | Description |
|-----------|-------------|
| firecrawl_deep_research | Conduct deep research on a query using web crawling, search, and AI analysis |
</Accordion>

<Note>For more details about tool input schema, use the [get_tools](https://docs.klavis.ai/api-reference/mcp-server/get-tools) API.</Note>

## Next Steps

<CardGroup>
  <Card
    title="AI Platform Integrations"
    icon="robot"
    href="/documentation/ai-platform-integration/overview"
  >
    Integrate Klavis MCP Servers with leading AI platforms
  </Card>
  
  <Card
    title="Integrations"
    icon="puzzle-piece"
    href="/documentation/mcp-server/overview"
  >
    Explore available MCP servers
  </Card>
  
  <Card
    title="API Reference"
    icon="code"
    href="/api-reference/introduction"
  >
    REST endpoints and schemas
  </Card>
</CardGroup>