---
title: 'Firecrawl Web Search'
description: 'Connect AI agents to Firecrawl Web Search for advanced web scraping and content extraction'
---

<Info>
  **Prerequisites**
  Before you begin, [create an account](https://www.klavis.ai/home/<USER>
</Info>

## Getting started

Connect to Firecrawl Web Search to perform advanced web scraping, content extraction, and automated web crawling through AI agents.

<Tabs>
  <Tab title="API">
    <Steps>
      <Step title="Install the SDKs (optional)">
        <CodeGroup>
          ```bash pip
          pip install klavis
          ```
          ```bash npm
          npm install klavis
          ```
        </CodeGroup>
      </Step>
      
      <Step title="Create a server instance">
        <CodeGroup>
          ```python Python
          from klavis import Klavis
          from klavis.types import McpServerName
          
          klavis_client = Klavis(api_key="YOUR_API_KEY")
          
          # Create a Firecrawl Web Search MCP server instance
          firecrawl_server = klavis_client.mcp_server.create_server_instance(
              server_name=McpServerName.FIRECRAWL_WEB_SEARCH,
              user_id="user123"
          )
          ```
          
          ```typescript TypeScript
          import { KlavisClient, Klavis } from 'klavis';
          
          const klavis = new KlavisClient({ apiKey: 'YOUR_API_KEY' });
          
          // Create a Firecrawl Web Search MCP server instance
          const firecrawlServer = await klavis.mcpServer.createServerInstance({
              serverName: Klavis.McpServerName.FirecrawlWebSearch,
              userId: "user123"
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/create" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "serverName": "Firecrawl Web Search",
              "userId": "user123"
            }'
          ```
        </CodeGroup>
        
        <Info>
          **Response Information**: The API returns:
          - `serverUrl`: The URL for connecting your MCP client to Firecrawl Web Search
          - `instanceId`: Unique identifier for your server instance
          - Note: Firecrawl requires an API key which should be configured after creation
        </Info>
      </Step>
      
      <Step title="Configure authentication">
        <Info>
          You can get your Firecrawl API key from the [Firecrawl website](https://firecrawl.dev/).
        </Info>
        
        <CodeGroup>
          ```python Python
          # Set the Firecrawl API key for your instance
          response = klavis_client.mcp_server.set_instance_auth(
              instance_id=firecrawl_server.instance_id,
              auth_data={
                  "token": "YOUR_FIRECRAWL_API_KEY"
              }
          )
          ```
          
          ```typescript TypeScript
          // Set the Firecrawl API key for your instance
          const response = await klavis.mcpServer.setInstanceAuth({
              instanceId: firecrawlServer.instanceId,
              authData: {
                  token: "YOUR_FIRECRAWL_API_KEY"
              }
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/set-auth" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "instanceId": "YOUR_INSTANCE_ID",
              "authData": {
                "token": "YOUR_FIRECRAWL_API_KEY"
              }
            }'
          ```
        </CodeGroup>
        
        <Check>
          🎉 **Your Firecrawl Web Search MCP Server is ready!** You can now use your MCP server URL with any MCP-compatible client to perform web scraping and extraction.
        </Check>
      </Step>
      

    </Steps>
  </Tab>
  
  <Tab title="UI">
    <Steps>
      <Step title="Open Dashboard">
        Go to your [Dashboard](https://www.klavis.ai/home/<USER>
      </Step>
      
      <Step title="Choose Firecrawl Web Search">
        Select Firecrawl Web Search from the list of available integrations.
      </Step>
      
      <Step title="Configure API Key">
        Enter your Firecrawl API key to enable web scraping functionality.
      </Step>
      
      <Step title="Use in your app">
        Copy the MCP endpoint URL and add it to your MCP-supported client (Claude Desktop, Cursor, VS Code, etc.).
      </Step>
    </Steps>
  </Tab>
  
  <Tab title="Open Source">
    <Steps>
      <Step title="Clone repository (for developers to build from source)">
        ```bash
        git clone https://github.com/klavis-ai/klavis
        cd klavis/mcp_servers/firecrawl
        ```
      </Step>
      
      <Step title="Run with Docker">
        ```bash
        # Pull the Docker image
        docker pull ghcr.io/klavis-ai/firecrawl-web-search-mcp-server:latest
        
        # Run with Firecrawl API key
        docker run -p 5000:5000 \
          -e AUTH_DATA='{"token":"your_firecrawl_api_key"}' \
          ghcr.io/klavis-ai/firecrawl-web-search-mcp-server:latest
        ```
      </Step>
      
      <Step title="Configure MCP client">
        ```json
        {
          "mcpServers": {
            "firecrawl": {
              "url": "http://localhost:5000/mcp/"
            }
          }
        }
        ```
      </Step>
    </Steps>
  </Tab>
</Tabs>

## Available Tools

<Accordion title="Tools Information">
| Tool Name | Description |
|-----------|-------------|
| firecrawl_scrape | Scrape a single webpage with advanced options for content extraction. Supports various formats including markdown, HTML, and screenshots. Can execute custom actions like clicking or scrolling before scraping. |
| firecrawl_map | Discover URLs from a starting point. Can use both sitemap.xml and HTML link discovery. |
| firecrawl_crawl | Start an asynchronous crawl of multiple pages from a starting URL. Supports depth control, path filtering, and webhook notifications. |
| firecrawl_check_crawl_status | Check the status of a crawl job. |
| firecrawl_batch_scrape | Scrape multiple URLs in batch mode. Returns a job ID that can be used to check status. |
| firecrawl_check_batch_status | Check the status of a batch scraping job. |
| firecrawl_search | Search and retrieve content from web pages with optional scraping. Returns SERP results by default (url, title, description) or full page content when scrapeOptions are provided. |
| firecrawl_extract | Extract structured information from web pages using LLM. Supports both cloud AI and self-hosted LLM extraction. |
| firecrawl_generate_llmstxt | Generate standardized LLMs.txt file for a given URL, which provides context about how LLMs should interact with the website. |
</Accordion>

<Note>For more details about tool input schema, use the [get_tools](https://docs.klavis.ai/api-reference/mcp-server/get-tools) API.</Note>

## Next Steps

<CardGroup>
  <Card
    title="AI Platform Integrations"
    icon="robot"
    href="/documentation/ai-platform-integration/overview"
  >
    Integrate Klavis MCP Servers with leading AI platforms
  </Card>
  
  <Card
    title="Integrations"
    icon="puzzle-piece"
    href="/documentation/mcp-server/overview"
  >
    Explore available MCP servers
  </Card>
  
  <Card
    title="API Reference"
    icon="code"
    href="/api-reference/introduction"
  >
    REST endpoints and schemas
  </Card>
</CardGroup>