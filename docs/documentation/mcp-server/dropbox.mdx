---
title: 'Dropbox'
description: 'Connect AI agents to Dropbox for file storage and collaboration management'
---

<Info>
  **Prerequisites**
  Before you begin, [create an account](https://www.klavis.ai/home/<USER>
</Info>

## Getting started

Connect to Dropbox to manage files, organize folders, share content, and automate file operations through AI agents.

<Tabs>
  <Tab title="API">
    <Steps>
      <Step title="Install the SDKs (optional)">
        <CodeGroup>
          ```bash pip
          pip install klavis
          ```
          ```bash npm
          npm install klavis
          ```
        </CodeGroup>
      </Step>
      
      <Step title="Create a server instance">
        <CodeGroup>
          ```python Python
          from klavis import Klavis
          from klavis.types import McpServerName
          
          klavis_client = Klavis(api_key="YOUR_API_KEY")
          
          # Create a Dropbox MCP server instance
          dropbox_server = klavis_client.mcp_server.create_server_instance(
              server_name=McpServerName.DROPBOX,
              user_id="user123"
          )
          ```
          
          ```typescript TypeScript
          import { KlavisC<PERSON>, Klavis } from 'klavis';
          
          const klavis = new KlavisClient({ apiKey: 'YOUR_API_KEY' });
          
          // Create a Dropbox MCP server instance
          const dropboxServer = await klavis.mcpServer.createServerInstance({
              serverName: Klavis.McpServerName.Dropbox,
              userId: "user123"
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/create" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "serverName": "Dropbox",
              "userId": "user123"
            }'
          ```
        </CodeGroup>
        
        <Info>
          **Response Information**: The API returns:
          - `serverUrl`: The URL for connecting your MCP client to Dropbox
          - `instanceId`: Unique identifier for your server instance
          - `oauthUrl`: OAuth authorization URL for Dropbox authentication
        </Info>
      </Step>
      
      <Step title="Authenticate">
        <CodeGroup>
          ```python Python
          import webbrowser
          
          # Open OAuth authorization page
          webbrowser.open(dropbox_server.oauth_url)
          ```
          
          ```typescript TypeScript
          // Redirect user to OAuth authorization
          window.location.href = dropboxServer.oauthUrl;
          ```
          
          ```bash cURL
          # Copy and paste the OAuth URL into your browser
          echo "Visit this URL to authorize: https://api.klavis.ai/oauth/dropbox/authorize?instance_id=YOUR_INSTANCE_ID"
          ```
        </CodeGroup>
        
        <Check>
          🎉 **Your Dropbox MCP Server is ready!** Once authentication is complete, you can use your MCP server URL with any MCP-compatible client.
        </Check>
      </Step>
      

    </Steps>
  </Tab>
  
  <Tab title="UI">
    <Steps>
      <Step title="Open Dashboard">
        Go to your [Dashboard](https://www.klavis.ai/home/<USER>
      </Step>
      
      <Step title="Choose Dropbox">
        Select Dropbox from the list of available integrations.
      </Step>
      
      <Step title="Authenticate">
        Complete the OAuth flow to connect your Dropbox account.
      </Step>
      
      <Step title="Use in your app">
        Copy the MCP endpoint URL and add it to your MCP-supported client (Claude Desktop, Cursor, VS Code, etc.).
      </Step>
    </Steps>
  </Tab>
  
  <Tab title="Open Source">
    <Steps>
      <Step title="Clone repository (for developers to build from source)">
        ```bash
        git clone https://github.com/klavis-ai/klavis
        cd klavis/mcp_servers/dropbox
        ```
      </Step>
      
      <Step title="Run with Docker">
        ```bash
        # Pull the Docker image
        docker pull ghcr.io/klavis-ai/dropbox-mcp-server:latest
        
        # Run with OAuth support (requires Klavis API key)
        docker run -p 5000:5000 \
          -e KLAVIS_API_KEY=$KLAVIS_API_KEY \
          ghcr.io/klavis-ai/dropbox-mcp-server:latest
        
        # Or run with manual API key
        docker run -p 5000:5000 \
          -e AUTH_DATA='{"access_token":"your_dropbox_token"}' \
          ghcr.io/klavis-ai/dropbox-mcp-server:latest
        ```
      </Step>
      
      <Step title="Configure MCP client">
        ```json
        {
          "mcpServers": {
            "dropbox": {
              "url": "http://localhost:5000/mcp/"
            }
          }
        }
        ```
      </Step>
    </Steps>
  </Tab>
</Tabs>

## Available Tools

<Accordion title="Tools Information">
| Tool Name | Description |
|-----------|-------------|
| dropbox_list_folder | Lists the contents of a folder |
| dropbox_continue_folder_listing | Continues listing folder contents using a cursor |
| dropbox_create_folder | Creates a new folder |
| dropbox_delete_file_folder | Deletes a file or folder |
| dropbox_move_file_folder | Moves or renames a file or folder |
| dropbox_copy_file_folder | Creates a copy of a file or folder |
| dropbox_batch_delete_files | Deletes multiple files and folders in a single operation |
| dropbox_batch_move_files | Moves or renames multiple files and folders in a single operation |
| dropbox_batch_copy_files | Creates copies of multiple files and folders in a single operation |
| dropbox_check_batch_job_status | Checks the status of a batch operation |
| dropbox_upload_file | Uploads a local file to Dropbox using file:// URI |
| dropbox_download_file | Downloads a file from Dropbox |
| dropbox_get_file_info | Gets metadata information about a file or folder |
| dropbox_list_revisions | Lists the revisions of a file |
| dropbox_restore_file | Restores a file to a previous revision |
| dropbox_search_files | Searches for files and folders |
| dropbox_continue_file_search | Continues searching files using a cursor |
| dropbox_get_shared_links | Lists shared links for files and folders |
| dropbox_share_file | Creates a shared link for a file or folder |
| dropbox_add_file_member | Adds a member to a file |
| dropbox_list_file_members | Lists the members of a file |
| dropbox_remove_file_member | Removes a member from a file |
| dropbox_share_folder | Shares a folder |
| dropbox_list_folder_members | Lists the members of a shared folder |
| dropbox_add_folder_member | Adds a member to a shared folder |
| dropbox_unshare_file | Removes all members from a file |
| dropbox_unshare_folder | Allows a shared folder owner to unshare the folder |
| dropbox_remove_folder_member | Removes a member from a shared folder |
| dropbox_create_file_request | Creates a file request |
| dropbox_get_file_request | Gets a file request by ID |
| dropbox_list_file_requests | Lists all file requests |
| dropbox_delete_file_request | Deletes file requests |
| dropbox_update_file_request | Updates a file request |
| dropbox_get_current_account | Gets information about the current account |
| dropbox_get_space_usage | Gets the current space usage information |
| dropbox_get_temporary_link | Gets a temporary link to a file |
| dropbox_get_file_thumbnail | Gets a thumbnail image for a file |
| dropbox_save_url_to_dropbox | Downloads content from a URL and saves it as a file in Dropbox |
| dropbox_check_url_save_status | Checks the status of a save URL operation |
| dropbox_lock_files_batch | Temporarily locks files to prevent them from being edited by others |
| dropbox_unlock_files_batch | Unlocks previously locked files |
| dropbox_list_received_files | Lists files that have been shared with the current user by others |
| dropbox_check_job_status | Checks the status of an asynchronous operation |
</Accordion>

<Note>For more details about tool input schema, use the [get_tools](https://docs.klavis.ai/api-reference/mcp-server/get-tools) API.</Note>

## Next Steps

<CardGroup>
  <Card
    title="White-label Authentication"
    icon="palette"
    href="/documentation/auth/white-label"
  >
    Customize OAuth flows with your own branding
  </Card>
  
  <Card
    title="AI Platform Integrations"
    icon="robot"
    href="/documentation/ai-platform-integration/overview"
  >
    Integrate Klavis MCP Servers with leading AI platforms
  </Card>
  
  <Card
    title="Integrations"
    icon="puzzle-piece"
    href="/documentation/mcp-server/overview"
  >
    Explore available MCP servers
  </Card>
  
  <Card
    title="API Reference"
    icon="code"
    href="/api-reference/introduction"
  >
    REST endpoints and schemas
  </Card>
</CardGroup> 