---
title: 'Brave Search'
description: 'Connect AI agents to Brave Search for web, image, news, and video search capabilities'
---

<Info>
  **Prerequisites**
  Before you begin, [create an account](https://www.klavis.ai/home/<USER>
</Info>

## Getting started

Connect to Brave Search to perform comprehensive web searches, including web results, images, news, and videos through AI agents.

<Tabs>
  <Tab title="API">
    <Steps>
      <Step title="Install the SDKs (optional)">
        <CodeGroup>
          ```bash pip
          pip install klavis
          ```
          ```bash npm
          npm install klavis
          ```
        </CodeGroup>
      </Step>
      
      <Step title="Create a server instance">
        <CodeGroup>
          ```python Python
          from klavis import Klavis
          from klavis.types import McpServerName
          
          klavis_client = Klavis(api_key="YOUR_API_KEY")
          
          # Create a Brave Search MCP server instance
          brave_search_server = klavis_client.mcp_server.create_server_instance(
              server_name=McpServerName.BRAVE_SEARCH,
              user_id="user123"
          )
          ```
          
          ```typescript TypeScript
          import { KlavisClient, Klavis } from 'klavis';
          
          const klavis = new KlavisClient({ apiKey: 'YOUR_API_KEY' });
          
          // Create a Brave Search MCP server instance
          const braveSearchServer = await klavis.mcpServer.createServerInstance({
              serverName: Klavis.McpServerName.BraveSearch,
              userId: "user123"
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/create" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "serverName": "BraveSearch",
              "userId": "user123"
            }'
          ```
        </CodeGroup>
        
        <Info>
          **Response Information**: The API returns:
          - `serverUrl`: The URL for connecting your MCP client to Brave Search
          - `instanceId`: Unique identifier for your server instance
          - Note: Brave Search requires an API key which should be configured after creation
        </Info>
      </Step>
      
      <Step title="Configure authentication">
        <CodeGroup>
          ```python Python
          # Set the Brave Search API key for your instance
          response = klavis_client.mcp_server.set_instance_auth(
              instance_id=brave_search_server.instance_id,
              auth_data={
                  "token": "YOUR_BRAVE_SEARCH_API_KEY"
              }
          )
          ```
          
          ```typescript TypeScript
          // Set the Brave Search API key for your instance
          const response = await klavis.mcpServer.setInstanceAuth({
              instanceId: braveSearchServer.instanceId,
              authData: {
                  token: "YOUR_BRAVE_SEARCH_API_KEY"
              }
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/set-auth" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "instanceId": "YOUR_INSTANCE_ID",
              "authData": {
                "token": "YOUR_BRAVE_SEARCH_API_KEY"
              }
            }'
          ```
        </CodeGroup>
        
        <Check>
          🎉 **Your Brave Search MCP Server is ready!** You can now use your MCP server URL with any MCP-compatible client to perform searches.
        </Check>
      </Step>
      
    </Steps>
  </Tab>
  
  <Tab title="UI">
    <Steps>
      <Step title="Open Dashboard">
        Go to your [Dashboard](https://www.klavis.ai/home/<USER>
      </Step>
      
      <Step title="Choose Brave Search">
        Select Brave Search from the list of available integrations.
      </Step>
      
      <Step title="Configure API Key">
        Enter your Brave Search API key to enable search functionality.
      </Step>
      
      <Step title="Use in your app">
        Copy the MCP endpoint URL and add it to your MCP-supported client (Claude Desktop, Cursor, VS Code, etc.).
      </Step>
    </Steps>
  </Tab>
  
  <Tab title="Open Source">
    <Steps>
      <Step title="Clone repository (for developers to build from source)">
        ```bash
        git clone https://github.com/klavis-ai/klavis
        cd klavis/mcp_servers/brave_search
        ```
      </Step>
      
      <Step title="Run with Docker">
        ```bash
        # Pull the Docker image
        docker pull ghcr.io/klavis-ai/brave-search-mcp-server:latest
        
        # Run with Brave Search API key
        docker run -p 5000:5000 \
          -e AUTH_DATA='{"token":"your_brave_search_api_key"}' \
          ghcr.io/klavis-ai/brave-search-mcp-server:latest
        ```
      </Step>
      
      <Step title="Configure MCP client">
        ```json
        {
          "mcpServers": {
            "brave_search": {
              "url": "http://localhost:5000/mcp/"
            }
          }
        }
        ```
      </Step>
    </Steps>
  </Tab>
</Tabs>

## Available Tools

<Accordion title="Tools Information">
| Tool Name | Description |
|-----------|-------------|
| brave_web_search | Perform a Brave web search with rich filtering and personalization options including country, language, safesearch, and pagination |
| brave_image_search | Search for images on Brave with support for safesearch filtering, language and country localization, and pagination |
| brave_news_search | Search for news articles with safesearch filtering, language and country localization, pagination, and freshness filter to get recent news |
| brave_video_search | Search for videos with safesearch filtering, language and country localization, pagination, and freshness filter to get recent videos |
</Accordion>

<Note>For more details about tool input schema, use the [get_tools](https://docs.klavis.ai/api-reference/mcp-server/get-tools) API.</Note>

## Next Steps

<CardGroup>
  <Card
    title="AI Platform Integrations"
    icon="robot"
    href="/documentation/ai-platform-integration/overview"
  >
    Integrate Klavis MCP Servers with leading AI platforms
  </Card>
  
  <Card
    title="Integrations"
    icon="puzzle-piece"
    href="/documentation/mcp-server/overview"
  >
    Explore available MCP servers
  </Card>
  
  <Card
    title="API Reference"
    icon="code"
    href="/api-reference/introduction"
  >
    REST endpoints and schemas
  </Card>
</CardGroup>