---
title: 'Markdown2doc'
description: 'Connect AI agents to Markdown2doc for converting markdown to various document formats'
---

<Info>
  **Prerequisites**
  Before you begin, [create an account](https://www.klavis.ai/home/<USER>
</Info>

## Getting started

Connect to Markdown2doc to convert markdown text to different file formats (PDF, DOCX, DOC, HTML, HTML5) through AI agents.

<Tabs>
  <Tab title="API">
    <Steps>
      <Step title="Install the SDKs (optional)">
        <CodeGroup>
          ```bash pip
          pip install klavis
          ```
          ```bash npm
          npm install klavis
          ```
        </CodeGroup>
      </Step>
      
      <Step title="Create a server instance">
        <CodeGroup>
          ```python Python
          from klavis import Klavis
          from klavis.types import McpServerName
          
          klavis_client = Klavis(api_key="YOUR_API_KEY")
          
          # Create a Markdown2doc MCP server instance
          markdown2doc_server = klavis_client.mcp_server.create_server_instance(
              server_name=McpServerName.MARKDOWN2DOC,
              user_id="user123"
          )
          ```
          
          ```typescript TypeScript
          import { KlavisClient, Klavis } from 'klavis';
          
          const klavis = new KlavisClient({ apiKey: 'YOUR_API_KEY' });
          
          // Create a Markdown2doc MCP server instance
          const markdown2docServer = await klavis.mcpServer.createServerInstance({
              serverName: Klavis.McpServerName.Markdown2doc,
              userId: "user123"
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/create" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "serverName": "Markdown2doc",
              "userId": "user123"
            }'
          ```
        </CodeGroup>
        
        <Info>
          **Response Information**: The API returns:
          - `serverUrl`: The URL for connecting your MCP client to Markdown2doc
          - `instanceId`: Unique identifier for your server instance
        </Info>
      </Step>
      
      <Step title="Configure API Key">
        <CodeGroup>
          ```python Python
          # Set the Markdown2doc API key for your instance
          response = klavis_client.mcp_server.set_instance_auth(
              instance_id=markdown2doc_server.instance_id,
              auth_data={
                  "api_key": "YOUR_MARKDOWN2DOC_API_KEY"
              }
          )
          ```
          
          ```typescript TypeScript
          // Set the Markdown2doc API key for your instance
          const response = await klavis.mcpServer.setInstanceAuth({
              instanceId: markdown2docServer.instanceId,
              authData: {
                  api_key: "YOUR_MARKDOWN2DOC_API_KEY"
              }
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/set-auth" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "instanceId": "YOUR_INSTANCE_ID",
              "authData": {
                "api_key": "YOUR_MARKDOWN2DOC_API_KEY"
              }
            }'
          ```
        </CodeGroup>
        
        <Check>
          🎉 **Your Markdown2doc MCP Server is ready!** You can now use your MCP server URL with any MCP-compatible client.
        </Check>
      </Step>
      

    </Steps>
  </Tab>
  
  <Tab title="UI">
    <Steps>
      <Step title="Open Dashboard">
        Go to your [Dashboard](https://www.klavis.ai/home/<USER>
      </Step>
      
      <Step title="Choose Markdown2doc">
        Select Markdown2doc from the list of available integrations.
      </Step>
      
      <Step title="Configure API Key">
        Enter your Markdown2doc API key to authenticate the connection.
      </Step>
      
      <Step title="Use in your app">
        Copy the MCP endpoint URL and add it to your MCP-supported client (Claude Desktop, Cursor, VS Code, etc.).
      </Step>
    </Steps>
  </Tab>
  
  <Tab title="Open Source">
    <Steps>
      <Step title="Clone repository (for developers to build from source)">
        ```bash
        git clone https://github.com/klavis-ai/klavis
        cd klavis/mcp_servers/markdown2doc
        ```
      </Step>
      
      <Step title="Run with Docker">
        ```bash
        # Pull the Docker image
        docker pull ghcr.io/klavis-ai/markdown2doc-mcp-server:latest
        
        # Run with API key
        docker run -p 5000:5000 \
          -e AUTH_DATA='{"api_key":"your_markdown2doc_api_key"}' \
          ghcr.io/klavis-ai/markdown2doc-mcp-server:latest
        ```
      </Step>
      
      <Step title="Configure MCP client">
        ```json
        {
          "mcpServers": {
            "markdown2doc": {
              "url": "http://localhost:5000/mcp/"
            }
          }
        }
        ```
      </Step>
    </Steps>
  </Tab>
</Tabs>

## Available Tools

<Accordion title="Tools Information">
| Tool Name              | Description                                                                 |
|------------------------|-----------------------------------------------------------------------------|
| convert_markdown_to_file | Convert markdown text to different file formats (pdf, docx, doc, html, html5) |
</Accordion>

<Note>For more details about tool input schema, use the [get_tools](https://docs.klavis.ai/api-reference/mcp-server/get-tools) API.</Note>

## Next Steps

<CardGroup>
  <Card
    title="AI Platform Integrations"
    icon="robot"
    href="/documentation/ai-platform-integration/overview"
  >
    Integrate Klavis MCP Servers with leading AI platforms
  </Card>
  
  <Card
    title="Integrations"
    icon="puzzle-piece"
    href="/documentation/mcp-server/overview"
  >
    Explore available MCP servers
  </Card>
  
  <Card
    title="API Reference"
    icon="code"
    href="/api-reference/introduction"
  >
    REST endpoints and schemas
  </Card>
</CardGroup>