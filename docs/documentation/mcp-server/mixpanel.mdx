---
title: 'Mixpanel'
description: 'Connect AI agents to Mixpanel for advanced analytics and user behavior tracking'
---

<Info>
  **Prerequisites**
  Before you begin, [create an account](https://www.klavis.ai/home/<USER>
</Info>

## Getting started

Connect to Mixpanel to track events, analyze user behavior, and generate insights from your product analytics through AI agents.

<Tabs>
  <Tab title="API">
    <Steps>
      <Step title="Install the SDKs (optional)">
        <CodeGroup>
          ```bash pip
          pip install klavis
          ```
          ```bash npm
          npm install klavis
          ```
        </CodeGroup>
      </Step>
      
      <Step title="Create a server instance">
        <CodeGroup>
          ```python Python
          from klavis import Klavis
          from klavis.types import McpServerName
          
          klavis_client = Klavis(api_key="YOUR_API_KEY")
          
          # Create a Mixpanel MCP server instance
          mixpanel_server = klavis_client.mcp_server.create_server_instance(
              server_name=McpServerName.MIXPANEL,
              user_id="user123"
          )
          ```
          
          ```typescript TypeScript
          import { KlavisClient, Klavis } from 'klavis';
          
          const klavis = new KlavisClient({ apiKey: 'YOUR_API_KEY' });
          
          // Create a Mixpanel MCP server instance
          const mixpanelServer = await klavis.mcpServer.createServerInstance({
              serverName: Klavis.McpServerName.Mixpanel,
              userId: "user123"
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/create" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "serverName": "Mixpanel",
              "userId": "user123"
            }'
          ```
        </CodeGroup>
        
        <Info>
          **Response Information**: The API returns:
          - `serverUrl`: The URL for connecting your MCP client to Mixpanel
          - `instanceId`: Unique identifier for your server instance
          - Note: Mixpanel requires service account credentials for authentication
        </Info>
      </Step>
      
      <Step title="Configure authentication">
        <Info>
          You can create service account credentials from your [Mixpanel project settings](https://mixpanel.com/settings/project/).
        </Info>
        
        <CodeGroup>
          ```python Python
          # Set the Mixpanel service account credentials for your instance
          response = klavis_client.mcp_server.set_instance_auth(
              instance_id=mixpanel_server.instance_id,
              auth_data={
                  "data": {
                      "serviceaccount_username": "YOUR_MIXPANEL_SERVICE_ACCOUNT_USERNAME",
                      "serviceaccount_secret": "YOUR_MIXPANEL_SERVICE_ACCOUNT_SECRET"
                  }
              }
          )
          ```
          
          ```typescript TypeScript
          // Set the Mixpanel service account credentials for your instance
          const response = await klavis.mcpServer.setInstanceAuth({
              instanceId: mixpanelServer.instanceId,
              authData: {
                  data: {
                      serviceaccount_username: "YOUR_MIXPANEL_SERVICE_ACCOUNT_USERNAME",
                      serviceaccount_secret: "YOUR_MIXPANEL_SERVICE_ACCOUNT_SECRET"
                  }
              }
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/set-auth" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "instanceId": "YOUR_INSTANCE_ID",
              "authData": {
                "data": {
                  "serviceaccount_username": "YOUR_MIXPANEL_SERVICE_ACCOUNT_USERNAME",
                  "serviceaccount_secret": "YOUR_MIXPANEL_SERVICE_ACCOUNT_SECRET"
                }
              }
            }'
          ```
        </CodeGroup>
        
        <Check>
          🎉 **Your Mixpanel MCP Server is ready!** You can now use your MCP server URL with any MCP-compatible client to analyze user behavior and track events.
        </Check>
      </Step>
      

    </Steps>
  </Tab>
  
  <Tab title="UI">
    <Steps>
      <Step title="Open Dashboard">
        Go to your [Dashboard](https://www.klavis.ai/home/<USER>
      </Step>
      
      <Step title="Choose Mixpanel">
        Select Mixpanel from the list of available integrations.
      </Step>
      
      <Step title="Configure Service Account">
        Enter your Mixpanel service account credentials to enable analytics access.
      </Step>
      
      <Step title="Use in your app">
        Copy the MCP endpoint URL and add it to your MCP-supported client (Claude Desktop, Cursor, VS Code, etc.).
      </Step>
    </Steps>
  </Tab>
  
  <Tab title="Open Source">
    <Steps>
      <Step title="Clone repository (for developers to build from source)">
        ```bash
        git clone https://github.com/klavis-ai/klavis
        cd klavis/mcp_servers/mixpanel
        ```
      </Step>
      
      <Step title="Run with Docker">
        ```bash
        # Pull the Docker image
        docker pull ghcr.io/klavis-ai/mixpanel-mcp-server:latest
        
        # Run with Mixpanel service account credentials
        docker run -p 5000:5000 \
          -e AUTH_DATA='{"data":{"serviceaccount_username":"your_username","serviceaccount_secret":"your_secret"}}' \
          ghcr.io/klavis-ai/mixpanel-mcp-server:latest
        ```
      </Step>
      
      <Step title="Configure MCP client">
        ```json
        {
          "mcpServers": {
            "mixpanel": {
              "url": "http://localhost:5000/mcp/"
            }
          }
        }
        ```
      </Step>
    </Steps>
  </Tab>
</Tabs>

## Available Tools

<Accordion title="Tools Information">
| Tool Name | Description |
|-----------|-------------|
| mixpanel_send_events | Send events to Mixpanel using the /import endpoint with Service Account authentication |
| mixpanel_get_projects | Get all projects that are accessible to the current service account user |
| mixpanel_get_events | Get event names for the given Mixpanel project |
| mixpanel_get_event_properties | Get event properties for the given event and Mixpanel project |
| mixpanel_get_event_property_values | Get event property values for the given event name, project, and property name |
| mixpanel_run_funnels_query | Run a funnel query to measure the conversion rate of a user journey |
| mixpanel_run_segmentation_query | Run a segmentation query to slice and dice your event stream for deeper insights |
| mixpanel_run_retention_query | Run a retention query to track user engagement over time and perform cohort analysis |
| mixpanel_run_frequency_query | Run a frequency query to analyze user engagement patterns and cohort behavior |
</Accordion>

<Note>For more details about tool input schema, use the [get_tools](https://docs.klavis.ai/api-reference/mcp-server/get-tools) API.</Note>

## Next Steps

<CardGroup>
  <Card
    title="AI Platform Integrations"
    icon="robot"
    href="/documentation/ai-platform-integration/overview"
  >
    Integrate Klavis MCP Servers with leading AI platforms
  </Card>
  
  <Card
    title="Integrations"
    icon="puzzle-piece"
    href="/documentation/mcp-server/overview"
  >
    Explore available MCP servers
  </Card>
  
  <Card
    title="API Reference"
    icon="code"
    href="/api-reference/introduction"
  >
    REST endpoints and schemas
  </Card>
</CardGroup>