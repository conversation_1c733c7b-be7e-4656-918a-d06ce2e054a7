---
title: 'Mailchimp'
description: 'Connect AI agents to Mailchimp for email marketing and audience management'
---

<Info>
  **Prerequisites**
  Before you begin, [create an account](https://www.klavis.ai/home/<USER>
</Info>

## Getting started

Connect to Mailchimp to manage email marketing campaigns, audiences, and subscriber data through AI agents.

<Tabs>
  <Tab title="API">
    <Steps>
      <Step title="Install the SDKs (optional)">
        <CodeGroup>
          ```bash pip
          pip install klavis
          ```
          ```bash npm
          npm install klavis
          ```
        </CodeGroup>
      </Step>
      
      <Step title="Create a server instance">
        <CodeGroup>
          ```python Python
          from klavis import Klavis
          from klavis.types import McpServerName
          
          klavis_client = Klavis(api_key="YOUR_API_KEY")
          
          # Create a Mailchimp MCP server instance
          mailchimp_server = klavis_client.mcp_server.create_server_instance(
              server_name=McpServerName.MAILCHIMP,
              user_id="user123"
          )
          ```
          
          ```typescript TypeScript
          import { KlavisClient, Klavis } from 'klavis';
          
          const klavis = new KlavisClient({ apiKey: 'YOUR_API_KEY' });
          
          // Create a Mailchimp MCP server instance
          const mailchimpServer = await klavis.mcpServer.createServerInstance({
              serverName: Klavis.McpServerName.Mailchimp,
              userId: "user123"
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/create" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "serverName": "Mailchimp",
              "userId": "user123"
            }'
          ```
        </CodeGroup>
        
        <Info>
          **Response Information**: The API returns:
          - `serverUrl`: The URL for connecting your MCP client to Mailchimp
          - `instanceId`: Unique identifier for your server instance
        </Info>
      </Step>
      
      <Step title="Configure API Key">
        <CodeGroup>
          ```python Python
          # Set the Mailchimp API key for your instance
          response = klavis_client.mcp_server.set_instance_auth(
              instance_id=mailchimp_server.instance_id,
              auth_data={
                  "data": {
                      "api_key": "YOUR_MAILCHIMP_API_KEY"
                  }
              }
          )
          ```
          
          ```typescript TypeScript
          // Set the Mailchimp API key for your instance
          const response = await klavis.mcpServer.setInstanceAuth({
              instanceId: mailchimpServer.instanceId,
              authData: {
                  data: {
                      api_key: "YOUR_MAILCHIMP_API_KEY"
                  }
              }
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/set-auth" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "instanceId": "YOUR_INSTANCE_ID",
              "authData": {
                "data": {
                  "api_key": "YOUR_MAILCHIMP_API_KEY"
                }
              }
            }'
          ```
        </CodeGroup>
        
        <Check>
          🎉 **Your Mailchimp MCP Server is ready!** You can now use your MCP server URL with any MCP-compatible client.
        </Check>
      </Step>
      

    </Steps>
  </Tab>
  
  <Tab title="UI">
    <Steps>
      <Step title="Open Dashboard">
        Go to your [Dashboard](https://www.klavis.ai/home/<USER>
      </Step>
      
      <Step title="Choose Mailchimp">
        Select Mailchimp from the list of available integrations.
      </Step>
      
      <Step title="Configure API Key">
        Enter your Mailchimp API key to authenticate the connection.
      </Step>
      
      <Step title="Use in your app">
        Copy the MCP endpoint URL and add it to your MCP-supported client (Claude Desktop, Cursor, VS Code, etc.).
      </Step>
    </Steps>
  </Tab>
  
  <Tab title="Open Source">
    <Steps>
      <Step title="Clone repository (for developers to build from source)">
        ```bash
        git clone https://github.com/klavis-ai/klavis
        cd klavis/mcp_servers/mailchimp
        ```
      </Step>
      
      <Step title="Run with Docker">
        ```bash
        # Pull the Docker image
        docker pull ghcr.io/klavis-ai/mailchimp-mcp-server:latest
        
        # Run with API key
        docker run -p 5000:5000 \
          -e AUTH_DATA='{"data":{"api_key":"your_mailchimp_api_key"}}' \
          ghcr.io/klavis-ai/mailchimp-mcp-server:latest
        ```
      </Step>
      
      <Step title="Configure MCP client">
        ```json
        {
          "mcpServers": {
            "mailchimp": {
              "url": "http://localhost:5000/mcp/"
            }
          }
        }
        ```
      </Step>
    </Steps>
  </Tab>
</Tabs>

## Available Tools

<Accordion title="Tools Information">
| Tool Name   | Description                                                              |
|-------------|--------------------------------------------------------------------------|
| mailchimp_ping | Test Mailchimp Connection - verify API authentication is working correctly |
| mailchimp_get_account_info | Get Account Information - retrieve comprehensive account details |
| mailchimp_get_all_audiences | Get All Audiences - retrieve all email lists in the account |
| mailchimp_create_audience | Create New Audience - create a new email list |
| mailchimp_get_audience_info | Get Audience Details - detailed information about a specific audience |
| mailchimp_update_audience | Update Audience Settings - modify audience configuration |
| mailchimp_delete_audience | Delete Audience - permanently delete an audience and all subscribers |
| mailchimp_get_audience_members | Get Audience Members - retrieve contacts from an audience |
| mailchimp_add_member_to_audience | Add Member to Audience - add a new contact to an audience |
| mailchimp_get_member_info | Get Member Details - detailed information about a specific member |
| mailchimp_update_member | Update Member - modify existing member information |
| mailchimp_delete_member | Delete Member - permanently remove a member from an audience |
| mailchimp_add_member_tags | Add Member Tags - add organizational tags to a member |
| mailchimp_remove_member_tags | Remove Member Tags - remove tags from a specific member |
| mailchimp_get_member_activity | Get Member Activity - retrieve recent activity history for a member |
| mailchimp_get_all_campaigns | Get All Campaigns - retrieve campaigns with optional filtering |
| mailchimp_create_campaign | Create Campaign - create a new email campaign in draft mode |
| mailchimp_get_campaign_info | Get Campaign Details - detailed information about a campaign |
| mailchimp_set_campaign_content | Set Campaign Content - add HTML content, plain text, or template to a campaign |
| mailchimp_send_campaign | Send Campaign - send a campaign immediately to all recipients |
| mailchimp_schedule_campaign | Schedule Campaign - schedule a campaign for delivery at a specific time |
| mailchimp_delete_campaign | Delete Campaign - permanently delete a draft campaign |
</Accordion>

<Note>For more details about tool input schema, use the [get_tools](https://docs.klavis.ai/api-reference/mcp-server/get-tools) API.</Note>

## Next Steps

<CardGroup>
  <Card
    title="AI Platform Integrations"
    icon="robot"
    href="/documentation/ai-platform-integration/overview"
  >
    Integrate Klavis MCP Servers with leading AI platforms
  </Card>
  
  <Card
    title="Integrations"
    icon="puzzle-piece"
    href="/documentation/mcp-server/overview"
  >
    Explore available MCP servers
  </Card>
  
  <Card
    title="API Reference"
    icon="code"
    href="/api-reference/introduction"
  >
    REST endpoints and schemas
  </Card>
</CardGroup>