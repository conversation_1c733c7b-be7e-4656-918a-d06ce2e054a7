---
title: 'Exa'
description: 'Connect AI agents to Exa for AI-powered semantic search and content discovery'
---

<Info>
  **Prerequisites**
  Before you begin, [create an account](https://www.klavis.ai/home/<USER>
</Info>

## Getting started

Connect to Exa to perform AI-powered semantic search, content retrieval, and comprehensive research through AI agents.

<Tabs>
  <Tab title="API">
    <Steps>
      <Step title="Install the SDKs (optional)">
        <CodeGroup>
          ```bash pip
          pip install klavis
          ```
          ```bash npm
          npm install klavis
          ```
        </CodeGroup>
      </Step>
      
      <Step title="Create a server instance">
        <CodeGroup>
          ```python Python
          from klavis import Klavis
          from klavis.types import McpServerName
          
          klavis_client = Klavis(api_key="YOUR_API_KEY")
          
          # Create an Exa MCP server instance
          exa_server = klavis_client.mcp_server.create_server_instance(
              server_name=McpServerName.EXA,
              user_id="user123"
          )
          ```
          
          ```typescript TypeScript
          import { KlavisClient, Klavis } from 'klavis';
          
          const klavis = new KlavisClient({ apiKey: 'YOUR_API_KEY' });
          
          // Create an Exa MCP server instance
          const exaServer = await klavis.mcpServer.createServerInstance({
              serverName: Klavis.McpServerName.Exa,
              userId: "user123"
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/create" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "serverName": "Exa",
              "userId": "user123"
            }'
          ```
        </CodeGroup>
        
        <Info>
          **Response Information**: The API returns:
          - `serverUrl`: The URL for connecting your MCP client to Exa
          - `instanceId`: Unique identifier for your server instance
          - Note: Exa requires an API key which should be configured after creation
        </Info>
      </Step>
      
      <Step title="Configure authentication">
        <Info>
          You can get your Exa API key from the [Exa AI website](https://docs.exa.ai/reference/getting-started).
        </Info>
        
        <CodeGroup>
          ```python Python
          # Set the Exa API key for your instance
          response = klavis_client.mcp_server.set_instance_auth(
              instance_id=exa_server.instance_id,
              auth_data={
                  "token": "YOUR_EXA_API_KEY"
              }
          )
          ```
          
          ```typescript TypeScript
          // Set the Exa API key for your instance
          const response = await klavis.mcpServer.setInstanceAuth({
              instanceId: exaServer.instanceId,
              authData: {
                  token: "YOUR_EXA_API_KEY"
              }
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/set-auth" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "instanceId": "YOUR_INSTANCE_ID",
              "authData": {
                "token": "YOUR_EXA_API_KEY"
              }
            }'
          ```
        </CodeGroup>
        
        <Check>
          🎉 **Your Exa MCP Server is ready!** You can now use your MCP server URL with any MCP-compatible client to perform semantic search.
        </Check>
      </Step>
      

    </Steps>
  </Tab>
  
  <Tab title="UI">
    <Steps>
      <Step title="Open Dashboard">
        Go to your [Dashboard](https://www.klavis.ai/home/<USER>
      </Step>
      
      <Step title="Choose Exa">
        Select Exa from the list of available integrations.
      </Step>
      
      <Step title="Configure API Key">
        Enter your Exa API key to enable semantic search functionality.
      </Step>
      
      <Step title="Use in your app">
        Copy the MCP endpoint URL and add it to your MCP-supported client (Claude Desktop, Cursor, VS Code, etc.).
      </Step>
    </Steps>
  </Tab>
  
  <Tab title="Open Source">
    <Steps>
      <Step title="Clone repository (for developers to build from source)">
        ```bash
        git clone https://github.com/klavis-ai/klavis
        cd klavis/mcp_servers/exa
        ```
      </Step>
      
      <Step title="Run with Docker">
        ```bash
        # Pull the Docker image
        docker pull ghcr.io/klavis-ai/exa-mcp-server:latest
        
        # Run with Exa API key
        docker run -p 5000:5000 \
          -e AUTH_DATA='{"token":"your_exa_api_key"}' \
          ghcr.io/klavis-ai/exa-mcp-server:latest
        ```
      </Step>
      
      <Step title="Configure MCP client">
        ```json
        {
          "mcpServers": {
            "exa": {
              "url": "http://localhost:5000/mcp/"
            }
          }
        }
        ```
      </Step>
    </Steps>
  </Tab>
</Tabs>

## Available Tools

<Accordion title="Tools Information">
| Tool Name | Description |
|-----------|-------------|
| exa_search | Perform AI-powered semantic search across the web using neural or keyword search with advanced filtering options including domains, dates, and content patterns |
| exa_get_contents | Retrieve clean, parsed HTML content from web pages using their Exa result IDs with optional highlighting and summarization |
| exa_find_similar | Discover web pages that are semantically similar to a given URL based on content meaning and context |
| exa_answer | Get direct, focused answers to specific questions by searching and analyzing web sources with citations |
| exa_research | Conduct comprehensive, multi-source research on topics with structured analysis and detailed citations |
</Accordion>

<Note>For more details about tool input schema, use the [get_tools](https://docs.klavis.ai/api-reference/mcp-server/get-tools) API.</Note>

## Next Steps

<CardGroup>
  <Card
    title="AI Platform Integrations"
    icon="robot"
    href="/documentation/ai-platform-integration/overview"
  >
    Integrate Klavis MCP Servers with leading AI platforms
  </Card>
  
  <Card
    title="Integrations"
    icon="puzzle-piece"
    href="/documentation/mcp-server/overview"
  >
    Explore available MCP servers
  </Card>
  
  <Card
    title="API Reference"
    icon="code"
    href="/api-reference/introduction"
  >
    REST endpoints and schemas
  </Card>
</CardGroup>
