---
title: 'Hacker News'
description: 'Connect AI agents to Hacker News for news aggregation and content analysis'
---

<Info>
  **Prerequisites**
  Before you begin, [create an account](https://www.klavis.ai/home/<USER>
</Info>

## Getting started

Connect to Hacker News to fetch stories, user profiles, and analyze technology news through AI agents.

<Tabs>
  <Tab title="API">
    <Steps>
      <Step title="Install the SDKs (optional)">
        <CodeGroup>
          ```bash pip
          pip install klavis
          ```
          ```bash npm
          npm install klavis
          ```
        </CodeGroup>
      </Step>
      
      <Step title="Create a server instance">
        <CodeGroup>
          ```python Python
          from klavis import Klavis
          from klavis.types import McpServerName
          
          klavis_client = Klavis(api_key="YOUR_API_KEY")
          
          # Create a Hacker News MCP server instance
          hacker_news_server = klavis_client.mcp_server.create_server_instance(
              server_name=McpServerName.HACKER_NEWS,
              user_id="user123"
          )
          ```
          
          ```typescript TypeScript
          import { KlavisClient, Klavis } from 'klavis';
          
          const klavis = new KlavisClient({ apiKey: 'YOUR_API_KEY' });
          
          // Create a Hacker News MCP server instance
          const hackerNewsServer = await klavis.mcpServer.createServerInstance({
              serverName: Klavis.McpServerName.HackerNews,
              userId: "user123"
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/create" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "serverName": "Hacker News",
              "userId": "user123"
            }'
          ```
        </CodeGroup>
        
        <Info>
          **Response Information**: The API returns:
          - `serverUrl`: The URL for connecting your MCP client to Hacker News
          - `instanceId`: Unique identifier for your server instance
        </Info>
        
        <Check>
          🎉 **Your Hacker News MCP Server is ready!** No additional authentication required - you can use your MCP server URL with any MCP-compatible client.
        </Check>
      </Step>
      

    </Steps>
  </Tab>
  
  <Tab title="UI">
    <Steps>
      <Step title="Open Dashboard">
        Go to your [Dashboard](https://www.klavis.ai/home/<USER>
      </Step>
      
      <Step title="Choose Hacker News">
        Select Hacker News from the list of available integrations.
      </Step>
      
      <Step title="Ready to use">
        No authentication required - your server is immediately ready.
      </Step>
      
      <Step title="Use in your app">
        Copy the MCP endpoint URL and add it to your MCP-supported client (Claude Desktop, Cursor, VS Code, etc.).
      </Step>
    </Steps>
  </Tab>
  
  <Tab title="Open Source">
    <Steps>
      <Step title="Clone repository (for developers to build from source)">
        ```bash
        git clone https://github.com/klavis-ai/klavis
        cd klavis/mcp_servers/hacker_news
        ```
      </Step>
      
      <Step title="Run with Docker">
        ```bash
        # Pull the Docker image
        docker pull ghcr.io/klavis-ai/hacker-news-mcp-server:latest
        
        # Run the server (no API key required for Hacker News)
        docker run -p 5000:5000 \
          ghcr.io/klavis-ai/hacker-news-mcp-server:latest
        ```
      </Step>
      
      <Step title="Configure MCP client">
        ```json
        {
          "mcpServers": {
            "hacker_news": {
              "url": "http://localhost:5000/mcp/"
            }
          }
        }
        ```
      </Step>
    </Steps>
  </Tab>
</Tabs>

## Available Tools

<Accordion title="Tools Information">
| Tool Name | Description |
|-----------|-------------|
| hackerNews_item | Fetches a single Hacker News item by its unique ID |
| hackerNews_user | Retrieves a Hacker News user's profile by their username |
| hackerNews_topstories | Fetches a list of the current top stories |
| hackerNews_beststories | Fetches a list of the all-time best stories |
| hackerNews_newstories | Fetches a list of the most recent stories |
| hackerNews_showstories | Fetches a list of the latest "Show HN" stories |
| hackerNews_askstories | Fetches a list of the latest "Ask HN" stories |
| hackerNews_jobstories | Fetches a list of the latest job postings |
| hackerNews_updates | Fetches recent updates, including new items and changed profiles |
</Accordion>

<Note>For more details about tool input schema, use the [get_tools](https://docs.klavis.ai/api-reference/mcp-server/get-tools) API.</Note>

## Next Steps

<CardGroup>
  <Card
    title="AI Platform Integrations"
    icon="robot"
    href="/documentation/ai-platform-integration/overview"
  >
    Integrate Klavis MCP Servers with leading AI platforms
  </Card>
  
  <Card
    title="Integrations"
    icon="puzzle-piece"
    href="/documentation/mcp-server/overview"
  >
    Explore available MCP servers
  </Card>
  
  <Card
    title="API Reference"
    icon="code"
    href="/api-reference/introduction"
  >
    REST endpoints and schemas
  </Card>
</CardGroup>