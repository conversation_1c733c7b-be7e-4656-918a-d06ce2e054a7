---
title: 'Google Docs'
description: 'Connect AI agents to Google Docs for document creation and collaboration'
---

<Info>
  **Prerequisites**
  Before you begin, [create an account](https://www.klavis.ai/home/<USER>
</Info>

## Getting started

Connect to Google Docs to create, edit, and manage Google Docs documents and collaborate on content through AI agents.

<Tabs>
  <Tab title="API">
    <Steps>
      <Step title="Install the SDKs (optional)">
        <CodeGroup>
          ```bash pip
          pip install klavis
          ```
          ```bash npm
          npm install klavis
          ```
        </CodeGroup>
      </Step>
      
      <Step title="Create a server instance">
        <CodeGroup>
          ```python Python
          from klavis import Klavis
          from klavis.types import McpServerName
          
          klavis_client = Klavis(api_key="YOUR_API_KEY")
          
          # Create a Google Docs MCP server instance
          google_docs_server = klavis_client.mcp_server.create_server_instance(
              server_name=McpServerName.GOOGLE_DOCS,
              user_id="user123"
          )
          ```
          
          ```typescript TypeScript
          import { KlavisClient, Klavis } from 'klavis';
          
          const klavis = new KlavisClient({ apiKey: 'YOUR_API_KEY' });
          
          // Create a Google Docs MCP server instance
          const googleDocsServer = await klavis.mcpServer.createServerInstance({
              serverName: Klavis.McpServerName.GoogleDocs,
              userId: "user123"
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/create" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "serverName": "Google Docs",
              "userId": "user123"
            }'
          ```
        </CodeGroup>
        
        <Info>
          **Response Information**: The API returns:
          - `serverUrl`: The URL for connecting your MCP client to Google Docs
          - `instanceId`: Unique identifier for your server instance
          - `oauthUrl`: OAuth authorization URL for Google Docs authentication
        </Info>
      </Step>
      
      <Step title="Authenticate">
        <CodeGroup>
          ```python Python
          import webbrowser
          
          # Open OAuth authorization page
          webbrowser.open(google_docs_server.oauth_url)
          ```
          
          ```typescript TypeScript
          // Redirect user to OAuth authorization
          window.location.href = googleDocsServer.oauthUrl;
          ```
          
          ```bash cURL
          # Copy and paste the OAuth URL into your browser
          echo "Visit this URL to authorize: https://api.klavis.ai/oauth/gdocs/authorize?instance_id=YOUR_INSTANCE_ID"
          ```
        </CodeGroup>
        
        <Check>
          🎉 **Your Google Docs MCP Server is ready!** Once authentication is complete, you can use your MCP server URL with any MCP-compatible client.
        </Check>
      </Step>
      

    </Steps>
  </Tab>
  
  <Tab title="UI">
    <Steps>
      <Step title="Open Dashboard">
        Go to your [Dashboard](https://www.klavis.ai/home/<USER>
      </Step>
      
      <Step title="Choose Google Docs">
        Select Google Docs from the list of available integrations.
      </Step>
      
      <Step title="Authenticate">
        Complete the OAuth flow to connect your Google Docs account.
      </Step>
      
      <Step title="Use in your app">
        Copy the MCP endpoint URL and add it to your MCP-supported client (Claude Desktop, Cursor, VS Code, etc.).
      </Step>
    </Steps>
  </Tab>
  
  <Tab title="Open Source">
    <Steps>
      <Step title="Clone repository (for developers to build from source)">
        ```bash
        git clone https://github.com/klavis-ai/klavis
        cd klavis/mcp_servers/google_docs
        ```
      </Step>
      
      <Step title="Run with Docker">
        ```bash
        # Pull the Docker image
        docker pull ghcr.io/klavis-ai/google-docs-mcp-server:latest
        
        # Run with OAuth support (requires Klavis API key)
        docker run -p 5000:5000 \
          -e KLAVIS_API_KEY=$KLAVIS_API_KEY \
          ghcr.io/klavis-ai/google-docs-mcp-server:latest
        ```
      </Step>
      
      <Step title="Configure MCP client">
        ```json
        {
          "mcpServers": {
            "google_docs": {
              "url": "http://localhost:5000/mcp/"
            }
          }
        }
        ```
      </Step>
    </Steps>
  </Tab>
</Tabs>

## Available Tools

<Accordion title="Tools Information">
| Tool Name                              | Description                                                              |
|----------------------------------------|--------------------------------------------------------------------------|
| google_docs_get_document_by_id         | Retrieve a Google Docs document by ID                                   |
| google_docs_get_all_documents          | Get all Google Docs documents from the user's Drive                     |
| google_docs_insert_text_at_end         | Insert text at the end of a Google Docs document                        |
| google_docs_create_blank_document      | Create a new blank Google Docs document with a title                    |
| google_docs_create_document_from_text  | Create a new Google Docs document with specified text content           |
</Accordion>

<Note>For more details about tool input schema, use the [get_tools](https://docs.klavis.ai/api-reference/mcp-server/get-tools) API.</Note>

## Next Steps

<CardGroup>
  <Card
    title="White-label Authentication"
    icon="palette"
    href="/documentation/auth/white-label"
  >
    Customize OAuth flows with your own branding
  </Card>
  
  <Card
    title="AI Platform Integrations"
    icon="robot"
    href="/documentation/ai-platform-integration/overview"
  >
    Integrate Klavis MCP Servers with leading AI platforms
  </Card>
  
  <Card
    title="Integrations"
    icon="puzzle-piece"
    href="/documentation/mcp-server/overview"
  >
    Explore available MCP servers
  </Card>
  
  <Card
    title="API Reference"
    icon="code"
    href="/api-reference/introduction"
  >
    REST endpoints and schemas
  </Card>
</CardGroup>