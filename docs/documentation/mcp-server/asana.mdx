---
title: 'Asana'
description: 'Connect AI agents to Asana for managing projects, tasks, and team collaboration'
---

<Info>
  **Prerequisites**
  Before you begin, [create an account](https://www.klavis.ai/home/<USER>
</Info>

## Getting started

Connect to Asana to manage projects, track tasks, and collaborate with your team through AI agents.

<Tabs>
  <Tab title="API">
    <Steps>
      <Step title="Install the SDKs (optional)">
        <CodeGroup>
          ```bash pip
          pip install klavis
          ```
          ```bash npm
          npm install klavis
          ```
        </CodeGroup>
      </Step>
      
      <Step title="Create a server instance">
        <CodeGroup>
          ```python Python
          from klavis import Klavis
          from klavis.types import McpServerName
          
          klavis_client = Klavis(api_key="YOUR_API_KEY")
          
          # Create an Asana MCP server instance
          asana_server = klavis_client.mcp_server.create_server_instance(
              server_name=McpServerName.ASANA,
              user_id="user123"
          )
          ```
          
          ```typescript TypeScript
          import { KlavisC<PERSON>, Klavis } from 'klavis';
          
          const klavis = new KlavisClient({ apiKey: 'YOUR_API_KEY' });
          
          // Create an Asana MCP server instance
          const asanaServer = await klavis.mcpServer.createServerInstance({
              serverName: Klavis.McpServerName.Asana,
              userId: "user123"
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/create" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "serverName": "Asana",
              "userId": "user123"
            }'
          ```
        </CodeGroup>
        
        <Info>
          **Response Information**: The API returns:
          - `serverUrl`: The URL for connecting your MCP client to Asana
          - `instanceId`: Unique identifier for your server instance
          - `oauthUrl`: OAuth authorization URL for Asana authentication
        </Info>
      </Step>
      
      <Step title="Authenticate">
        <CodeGroup>
          ```python Python
          import webbrowser
          
          # Open OAuth authorization page
          webbrowser.open(asana_server.oauth_url)
          ```
          
          ```typescript TypeScript
          // Redirect user to OAuth authorization
          window.location.href = asanaServer.oauthUrl;
          ```
          
          ```bash cURL
          # Copy and paste the OAuth URL into your browser
          echo "Visit this URL to authorize: https://api.klavis.ai/oauth/asana/authorize?instance_id=YOUR_INSTANCE_ID"
          ```
        </CodeGroup>
        
        <Check>
          🎉 **Your Asana MCP Server is ready!** Once authentication is complete, you can use your MCP server URL with any MCP-compatible client.
        </Check>
      </Step>
      
    </Steps>
  </Tab>
  
  <Tab title="UI">
    <Steps>
      <Step title="Open Dashboard">
        Go to your [Dashboard](https://www.klavis.ai/home/<USER>
      </Step>
      
      <Step title="Choose Asana">
        Select Asana from the list of available integrations.
      </Step>
      
      <Step title="Authenticate">
        Complete the OAuth flow to connect your Asana account.
      </Step>
      
      <Step title="Use in your app">
        Copy the MCP endpoint URL and add it to your MCP-supported client (Claude Desktop, Cursor, VS Code, etc.).
      </Step>
    </Steps>
  </Tab>
  
  <Tab title="Open Source">
    <Steps>
      <Step title="Clone repository (for developers to build from source)">
        ```bash
        git clone https://github.com/klavis-ai/klavis
        cd klavis/mcp_servers/asana
        ```
      </Step>
      
      <Step title="Run with Docker">
        ```bash
        # Pull the Docker image
        docker pull ghcr.io/klavis-ai/asana-mcp-server:latest
        
        # Run with OAuth support (requires Klavis API key)
        docker run -p 5000:5000 \
          -e KLAVIS_API_KEY=$KLAVIS_API_KEY \
          ghcr.io/klavis-ai/asana-mcp-server:latest
        
        # Or run with manual API key
        docker run -p 5000:5000 \
          -e AUTH_DATA='{"access_token":"your_asana_api_key"}' \
          ghcr.io/klavis-ai/asana-mcp-server:latest
        ```
      </Step>
      
      <Step title="Configure MCP client">
        ```json
        {
          "mcpServers": {
            "asana": {
              "url": "http://localhost:5000/mcp/"
            }
          }
        }
        ```
      </Step>
    </Steps>
  </Tab>
</Tabs>

## Available Tools

<Accordion title="Tools Information">
| Tool Name                    | Description                                                              |
|------------------------------|--------------------------------------------------------------------------|
| asana_get_current_user | Get information about the current user |
| asana_list_workspaces | List all workspaces |
| asana_list_projects | List all projects in a workspace |
| asana_get_project | Get details of a specific project |
| asana_list_tasks | List all tasks in a project |
| asana_get_task | Get details of a specific task |
| asana_create_task | Create a new task |
| asana_update_task | Update an existing task |
| asana_delete_task | Delete a task |
| asana_list_sections | List all sections in a project |
| asana_create_section | Create a new section |
| asana_list_teams | List all teams in a workspace |
| asana_list_team_members | List members of a specific team |
</Accordion>

<Note>For more details about tool input schema, use the [get_tools](https://docs.klavis.ai/api-reference/mcp-server/get-tools) API.</Note>

## Next Steps

<CardGroup>
  <Card
    title="White-label Authentication"
    icon="palette"
    href="/documentation/auth/white-label"
  >
    Customize OAuth flows with your own branding
  </Card>
  
  <Card
    title="AI Platform Integrations"
    icon="robot"
    href="/documentation/ai-platform-integration/overview"
  >
    Integrate Klavis MCP Servers with leading AI platforms
  </Card>
  
  <Card
    title="Integrations"
    icon="puzzle-piece"
    href="/documentation/mcp-server/overview"
  >
    Explore available MCP servers
  </Card>
  
  <Card
    title="API Reference"
    icon="code"
    href="/api-reference/introduction"
  >
    REST endpoints and schemas
  </Card>
</CardGroup>