---
title: 'Affinity'
description: 'Connect AI agents to Affinity CRM for managing contacts, deals, and relationships'
---

<Info>
  **Prerequisites**
  Before you begin, [create an account](https://www.klavis.ai/home/<USER>
</Info>

## Getting started

Connect to Affinity CRM to manage relationships, track deals, and organize contacts through AI agents.

<Tabs>
  <Tab title="API">
    <Steps>
      <Step title="Install the SDKs (optional)">
        <CodeGroup>
          ```bash pip
          pip install klavis
          ```
          ```bash npm
          npm install klavis
          ```
        </CodeGroup>
      </Step>
      
      <Step title="Create a server instance">
        <CodeGroup>
          ```python Python
          from klavis import Klavis
          from klavis.types import McpServerName
          
          klavis_client = Klavis(api_key="YOUR_API_KEY")
          
          # Create an Affinity MCP server instance
          affinity_server = klavis_client.mcp_server.create_server_instance(
              server_name=McpServerName.AFFINITY,
              user_id="user123"
          )
          ```
          
          ```typescript TypeScript
          import { KlavisClient, Klavis } from 'klavis';
          
          const klavis = new KlavisClient({ apiKey: 'YOUR_API_KEY' });
          
          // Create an Affinity MCP server instance
          const affinityServer = await klavis.mcpServer.createServerInstance({
              serverName: Klavis.McpServerName.Affinity,
              userId: "user123"
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/create" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "serverName": "Affinity",
              "userId": "user123"
            }'
          ```
        </CodeGroup>
        
        <Info>
          **Response Information**: The API returns:
          - `serverUrl`: The URL for connecting your MCP client to Affinity
          - `instanceId`: Unique identifier for your server instance
          - `oauthUrl`: OAuth authorization URL for Affinity authentication
        </Info>
      </Step>
      
      <Step title="Authenticate">
        <CodeGroup>
          ```python Python
          import webbrowser
          
          # Open OAuth authorization page
          webbrowser.open(affinity_server.oauth_url)
          ```
          
          ```typescript TypeScript
          // Redirect user to OAuth authorization
          window.location.href = affinityServer.oauthUrl;
          ```
          
          ```bash cURL
          # Copy and paste the OAuth URL into your browser
          echo "Visit this URL to authorize: https://api.klavis.ai/oauth/affinity/authorize?instance_id=YOUR_INSTANCE_ID"
          ```
        </CodeGroup>
        
        <Check>
          🎉 **Your Affinity MCP Server is ready!** Once authentication is complete, you can use your MCP server URL with any MCP-compatible client.
        </Check>
      </Step>
      
    </Steps>
  </Tab>
  
  <Tab title="UI">
    <Steps>
      <Step title="Open Dashboard">
        Go to your [Dashboard](https://www.klavis.ai/home/<USER>
      </Step>
      
      <Step title="Choose Affinity">
        Select Affinity from the list of available integrations.
      </Step>
      
      <Step title="Authenticate">
        Complete the OAuth flow to connect your Affinity account.
      </Step>
      
      <Step title="Use in your app">
        Copy the MCP endpoint URL and add it to your MCP-supported client (Claude Desktop, Cursor, VS Code, etc.).
      </Step>
    </Steps>
  </Tab>
  
  <Tab title="Open Source">
    <Steps>
      <Step title="Clone repository (for developers to build from source)">
        ```bash
        git clone https://github.com/klavis-ai/klavis
        cd klavis/mcp_servers/affinity
        ```
      </Step>
      
      <Step title="Run with Docker">
        ```bash
        # Pull the Docker image
        docker pull ghcr.io/klavis-ai/affinity-mcp-server:latest
        
        # Run with OAuth support (requires Klavis API key)
        docker run -p 5000:5000 \
          -e KLAVIS_API_KEY=$KLAVIS_API_KEY \
          ghcr.io/klavis-ai/affinity-mcp-server:latest
        
        # Or run with manual API key
        docker run -p 5000:5000 \
          -e AUTH_DATA='{"access_token":"your_affinity_api_key"}' \
          ghcr.io/klavis-ai/affinity-mcp-server:latest
        ```
      </Step>
      
      <Step title="Configure MCP client">
        ```json
        {
          "mcpServers": {
            "affinity": {
              "url": "http://localhost:5000/mcp/"
            }
          }
        }
        ```
      </Step>
    </Steps>
  </Tab>
</Tabs>

## Available Tools

<Accordion title="Tools Information">
| Tool Name   | Description                                                              |
|-------------|--------------------------------------------------------------------------|
| affinity_get_current_user | Get current user information from Affinity |
| affinity_get_all_list_entries_on_a_list | Get all List Entries on a List |
| affinity_get_metadata_on_all_lists | Get metadata on all Lists |
| affinity_get_metadata_on_a_single_list | Get metadata on a single List |
| affinity_get_metadata_on_a_single_list_fields | Get metadata on a single List's Fields |
| affinity_get_a_single_list_entry_on_a_list | Get a single List Entry on a List |
| affinity_get_all_persons | Get all Persons in Affinity |
| affinity_get_single_person | Get a single Person by ID |
| affinity_get_person_fields_metadata | Get metadata on Person Fields |
| affinity_get_person_lists | Get a Person's Lists |
| affinity_get_person_list_entries | Get a Person's List Entries |
| affinity_get_all_companies | Get all Companies in Affinity with basic information and field data |
| affinity_get_single_company | Get a single Company by ID with basic information and field data |
| affinity_get_company_fields_metadata | Get metadata on Company Fields |
| affinity_get_company_lists | Get all Lists that contain the specified Company |
| affinity_get_company_list_entries | Get List Entries for a Company across all Lists with field data |
| affinity_get_all_opportunities | Get all Opportunities in Affinity |
| affinity_get_single_opportunity | Get a single Opportunity by ID |
| affinity_search_persons | Search for persons in Affinity. Search term can be part of an email address, first name, or last name |
| affinity_search_organizations | Search for organizations / companies in Affinity. Search term can be part of organization name or domain |
| affinity_search_opportunities | Search for opportunities in Affinity. Search term can be part of opportunity name |
| affinity_get_all_notes | Get all Notes in Affinity |
| affinity_get_specific_note | Get a specific note by ID |
</Accordion>

<Note>For more details about tool input schema, use the [get_tools](https://docs.klavis.ai/api-reference/mcp-server/get-tools) API.</Note>

## Next Steps

<CardGroup>
  <Card
    title="White-label Authentication"
    icon="palette"
    href="/documentation/auth/white-label"
  >
    Customize OAuth flows with your own branding
  </Card>
  
  <Card
    title="AI Platform Integrations"
    icon="robot"
    href="/documentation/ai-platform-integration/overview"
  >
    Integrate Klavis MCP Servers with leading AI platforms
  </Card>
  
  <Card
    title="Integrations"
    icon="puzzle-piece"
    href="/documentation/mcp-server/overview"
  >
    Explore available MCP servers
  </Card>
  
  <Card
    title="API Reference"
    icon="code"
    href="/api-reference/introduction"
  >
    REST endpoints and schemas
  </Card>
</CardGroup>