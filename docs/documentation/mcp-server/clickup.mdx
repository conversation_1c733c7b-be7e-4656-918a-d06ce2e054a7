---
title: 'ClickUp'
description: 'Connect AI agents to ClickUp for project management and task tracking'
---

<Info>
  **Prerequisites**
  Before you begin, [create an account](https://www.klavis.ai/home/<USER>
</Info>

## Getting started

Connect to ClickUp to manage projects, track tasks, and automate workflow operations through AI agents.

<Tabs>
  <Tab title="API">
    <Steps>
      <Step title="Install the SDKs (optional)">
        <CodeGroup>
          ```bash pip
          pip install klavis
          ```
          ```bash npm
          npm install klavis
          ```
        </CodeGroup>
      </Step>
      
      <Step title="Create a server instance">
        <CodeGroup>
          ```python Python
          from klavis import Klavis
          from klavis.types import McpServerName
          
          klavis_client = Klavis(api_key="YOUR_API_KEY")
          
          # Create a ClickUp MCP server instance
          clickup_server = klavis_client.mcp_server.create_server_instance(
              server_name=McpServerName.CLICKUP,
              user_id="user123"
          )
          ```
          
          ```typescript TypeScript
          import { KlavisClient, Klavis } from 'klavis';
          
          const klavis = new KlavisClient({ apiKey: 'YOUR_API_KEY' });
          
          // Create a ClickUp MCP server instance
          const clickupServer = await klavis.mcpServer.createServerInstance({
              serverName: Klavis.McpServerName.ClickUp,
              userId: "user123"
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/create" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "serverName": "ClickUp",
              "userId": "user123"
            }'
          ```
        </CodeGroup>
        
        <Info>
          **Response Information**: The API returns:
          - `serverUrl`: The URL for connecting your MCP client to ClickUp
          - `instanceId`: Unique identifier for your server instance
          - `oauthUrl`: OAuth authorization URL for ClickUp authentication
        </Info>
      </Step>
      
      <Step title="Authenticate">
        <CodeGroup>
          ```python Python
          import webbrowser
          
          # Open OAuth authorization page
          webbrowser.open(clickup_server.oauth_url)
          ```
          
          ```typescript TypeScript
          // Redirect user to OAuth authorization
          window.location.href = clickupServer.oauthUrl;
          ```
          
          ```bash cURL
          # Copy and paste the OAuth URL into your browser
          echo "Visit this URL to authorize: https://api.klavis.ai/oauth/clickup/authorize?instance_id=YOUR_INSTANCE_ID"
          ```
        </CodeGroup>
        
        <Check>
          🎉 **Your ClickUp MCP Server is ready!** Once authentication is complete, you can use your MCP server URL with any MCP-compatible client.
        </Check>
      </Step>
      

    </Steps>
  </Tab>
  
  <Tab title="UI">
    <Steps>
      <Step title="Open Dashboard">
        Go to your [Dashboard](https://www.klavis.ai/home/<USER>
      </Step>
      
      <Step title="Choose ClickUp">
        Select ClickUp from the list of available integrations.
      </Step>
      
      <Step title="Authenticate">
        Complete the OAuth flow to connect your ClickUp workspace.
      </Step>
      
      <Step title="Use in your app">
        Copy the MCP endpoint URL and add it to your MCP-supported client (Claude Desktop, Cursor, VS Code, etc.).
      </Step>
    </Steps>
  </Tab>
  
  <Tab title="Open Source">
    <Steps>
      <Step title="Clone repository (for developers to build from source)">
        ```bash
        git clone https://github.com/klavis-ai/klavis
        cd klavis/mcp_servers/clickup
        ```
      </Step>
      
      <Step title="Run with Docker">
        ```bash
        # Pull the Docker image
        docker pull ghcr.io/klavis-ai/clickup-mcp-server:latest
        
        # Run with OAuth support (requires Klavis API key)
        docker run -p 5000:5000 \
          -e KLAVIS_API_KEY=$KLAVIS_API_KEY \
          ghcr.io/klavis-ai/clickup-mcp-server:latest
        
        # Or run with manual API key
        docker run -p 5000:5000 \
          -e AUTH_DATA='{"access_token":"your_clickup_token"}' \
          ghcr.io/klavis-ai/clickup-mcp-server:latest
        ```
      </Step>
      
      <Step title="Configure MCP client">
        ```json
        {
          "mcpServers": {
            "clickup": {
              "url": "http://localhost:5000/mcp/"
            }
          }
        }
        ```
      </Step>
    </Steps>
  </Tab>
</Tabs>

## Available Tools

<Accordion title="Tools Information">
| Tool Name | Description |
|-----------|-------------|
| clickup_get_teams | Get all teams/workspaces the user has access to |
| clickup_get_workspaces | Get all workspaces (alias for get_teams) |
| clickup_get_spaces | Get all spaces in a team |
| clickup_create_space | Create a new space in a team |
| clickup_update_space | Update an existing space |
| clickup_get_folders | Get all folders in a space |
| clickup_create_folder | Create a new folder in a space |
| clickup_update_folder | Update an existing folder |
| clickup_get_lists | Get all lists in a folder or space. Either folder_id or space_id must be provided |
| clickup_create_list | Create a new list in a folder or space. Either folder_id or space_id must be provided along with name |
| clickup_update_list | Update an existing list |
| clickup_get_tasks | Get tasks from a list with optional filtering |
| clickup_get_task_by_id | Get a specific task by ID |
| clickup_create_task | Create a new task in ClickUp |
| clickup_update_task | Update an existing task in ClickUp |
| clickup_search_tasks | Search for tasks by text query |
| clickup_get_comments | Get comments for a specific task |
| clickup_create_comment | Create a comment on a task |
| clickup_update_comment | Update an existing comment |
| clickup_get_user | Get the current user's information |
| clickup_get_team_members | Get all team members |
</Accordion>

<Note>For more details about tool input schema, use the [get_tools](https://docs.klavis.ai/api-reference/mcp-server/get-tools) API.</Note>

## Next Steps

<CardGroup>
  <Card
    title="White-label Authentication"
    icon="palette"
    href="/documentation/auth/white-label"
  >
    Customize OAuth flows with your own branding
  </Card>
  
  <Card
    title="AI Platform Integrations"
    icon="robot"
    href="/documentation/ai-platform-integration/overview"
  >
    Integrate Klavis MCP Servers with leading AI platforms
  </Card>
  
  <Card
    title="Integrations"
    icon="puzzle-piece"
    href="/documentation/mcp-server/overview"
  >
    Explore available MCP servers
  </Card>
  
  <Card
    title="API Reference"
    icon="code"
    href="/api-reference/introduction"
  >
    REST endpoints and schemas
  </Card>
</CardGroup> 