---
title: 'Linear'
description: 'Connect AI agents to Linear for project management and issue tracking automation'
---

<Info>
  **Prerequisites**
  Before you begin, [create an account](https://www.klavis.ai/home/<USER>
</Info>

## Getting started

Connect to Linear to manage issues, projects, teams, and automate project management workflows through AI agents.

<Tabs>
  <Tab title="API">
    <Steps>
      <Step title="Install the SDKs (optional)">
        <CodeGroup>
          ```bash pip
          pip install klavis
          ```
          ```bash npm
          npm install klavis
          ```
        </CodeGroup>
      </Step>
      
      <Step title="Create a server instance">
        <CodeGroup>
          ```python Python
          from klavis import Klavis
          from klavis.types import McpServerName
          
          klavis_client = Klavis(api_key="YOUR_API_KEY")
          
          # Create a Linear MCP server instance
          linear_server = klavis_client.mcp_server.create_server_instance(
              server_name=McpServerName.LINEAR,
              user_id="user123"
          )
          ```
          
          ```typescript TypeScript
          import { KlavisClient, Klavis } from 'klavis';
          
          const klavis = new KlavisClient({ apiKey: 'YOUR_API_KEY' });
          
          // Create a Linear MCP server instance
          const linearServer = await klavis.mcpServer.createServerInstance({
              serverName: Klavis.McpServerName.Linear,
              userId: "user123"
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/create" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "serverName": "Linear",
              "userId": "user123"
            }'
          ```
        </CodeGroup>
        
        <Info>
          **Response Information**: The API returns:
          - `serverUrl`: The URL for connecting your MCP client to Linear
          - `instanceId`: Unique identifier for your server instance
          - `oauthUrl`: OAuth authorization URL for Linear authentication
        </Info>
      </Step>
      
      <Step title="Authenticate">
        <CodeGroup>
          ```python Python
          import webbrowser
          
          # Open OAuth authorization page
          webbrowser.open(linear_server.oauth_url)
          ```
          
          ```typescript TypeScript
          // Redirect user to OAuth authorization
          window.location.href = linearServer.oauthUrl;
          ```
          
          ```bash cURL
          # Copy and paste the OAuth URL into your browser
          echo "Visit this URL to authorize: https://api.klavis.ai/oauth/linear/authorize?instance_id=YOUR_INSTANCE_ID"
          ```
        </CodeGroup>
        
        <Check>
          🎉 **Your Linear MCP Server is ready!** Once authentication is complete, you can use your MCP server URL with any MCP-compatible client.
        </Check>
      </Step>
      

    </Steps>
  </Tab>
  
  <Tab title="UI">
    <Steps>
      <Step title="Open Dashboard">
        Go to your [Dashboard](https://www.klavis.ai/home/<USER>
      </Step>
      
      <Step title="Choose Linear">
        Select Linear from the list of available integrations.
      </Step>
      
      <Step title="Authenticate">
        Complete the OAuth flow to connect your Linear workspace.
      </Step>
      
      <Step title="Use in your app">
        Copy the MCP endpoint URL and add it to your MCP-supported client (Claude Desktop, Cursor, VS Code, etc.).
      </Step>
    </Steps>
  </Tab>
  
  <Tab title="Open Source">
    <Steps>
      <Step title="Clone repository (for developers to build from source)">
        ```bash
        git clone https://github.com/klavis-ai/klavis
        cd klavis/mcp_servers/linear
        ```
      </Step>
      
      <Step title="Run with Docker">
        ```bash
        # Pull the Docker image
        docker pull ghcr.io/klavis-ai/linear-mcp-server:latest
        
        # Run with OAuth support (requires Klavis API key)
        docker run -p 5000:5000 \
          -e KLAVIS_API_KEY=$KLAVIS_API_KEY \
          ghcr.io/klavis-ai/linear-mcp-server:latest
        ```
      </Step>
      
      <Step title="Configure MCP client">
        ```json
        {
          "mcpServers": {
            "linear": {
              "url": "http://localhost:5000/mcp/"
            }
          }
        }
        ```
      </Step>
    </Steps>
  </Tab>
</Tabs>

## Available Tools

<Accordion title="Tools Information">
| Tool Name | Description |
|-----------|-------------|
| linear_get_teams | Get all teams in the Linear workspace |
| linear_get_issues | Get issues, optionally filtered by team |
| linear_get_issue_by_id | Get a specific issue by its ID |
| linear_create_issue | Create a new issue in Linear |
| linear_update_issue | Update an existing issue in Linear |
| linear_get_projects | Get projects, optionally filtered by team |
| linear_create_project | Create a new project in Linear |
| linear_update_project | Update an existing project in Linear |
| linear_get_comments | Get comments for a specific issue |
| linear_create_comment | Create a comment on an issue |
| linear_update_comment | Update an existing comment |
| linear_search_issues | Search for issues by text query |
</Accordion>

<Note>For more details about tool input schema, use the [get_tools](https://docs.klavis.ai/api-reference/mcp-server/get-tools) API.</Note>

## Next Steps

<CardGroup>
  <Card
    title="White-label Authentication"
    icon="palette"
    href="/documentation/auth/white-label"
  >
    Customize OAuth flows with your own branding
  </Card>
  
  <Card
    title="AI Platform Integrations"
    icon="robot"
    href="/documentation/ai-platform-integration/overview"
  >
    Integrate Klavis MCP Servers with leading AI platforms
  </Card>
  
  <Card
    title="Integrations"
    icon="puzzle-piece"
    href="/documentation/mcp-server/overview"
  >
    Explore available MCP servers
  </Card>
  
  <Card
    title="API Reference"
    icon="code"
    href="/api-reference/introduction"
  >
    REST endpoints and schemas
  </Card>
</CardGroup>