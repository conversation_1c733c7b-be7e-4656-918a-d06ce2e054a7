---
title: 'HubSpot'
description: 'Connect AI agents to HubSpot for CRM management and sales automation'
---

<Info>
  **Prerequisites**
  Before you begin, [create an account](https://www.klavis.ai/home/<USER>
</Info>

## Getting started

Connect to HubSpot to manage contacts, companies, deals, tickets, and automate CRM operations through AI agents.

<Tabs>
  <Tab title="API">
    <Steps>
      <Step title="Install the SDKs (optional)">
        <CodeGroup>
          ```bash pip
          pip install klavis
          ```
          ```bash npm
          npm install klavis
          ```
        </CodeGroup>
      </Step>
      
      <Step title="Create a server instance">
        <CodeGroup>
          ```python Python
          from klavis import Klavis
          from klavis.types import McpServerName
          
          klavis_client = Klavis(api_key="YOUR_API_KEY")
          
          # Create a HubSpot MCP server instance
          hubspot_server = klavis_client.mcp_server.create_server_instance(
              server_name=McpServerName.HUBSPOT,
              user_id="user123"
          )
          ```
          
          ```typescript TypeScript
          import { KlavisClient, Klavis } from 'klavis';
          
          const klavis = new KlavisClient({ apiKey: 'YOUR_API_KEY' });
          
          // Create a HubSpot MCP server instance
          const hubspotServer = await klavis.mcpServer.createServerInstance({
              serverName: Klavis.McpServerName.Hubspot,
              userId: "user123"
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/create" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "serverName": "HubSpot",
              "userId": "user123"
            }'
          ```
        </CodeGroup>
        
        <Info>
          **Response Information**: The API returns:
          - `serverUrl`: The URL for connecting your MCP client to HubSpot
          - `instanceId`: Unique identifier for your server instance
          - `oauthUrl`: OAuth authorization URL for HubSpot authentication
        </Info>
      </Step>
      
      <Step title="Authenticate">
        <CodeGroup>
          ```python Python
          import webbrowser
          
          # Open OAuth authorization page
          webbrowser.open(hubspot_server.oauth_url)
          ```
          
          ```typescript TypeScript
          // Redirect user to OAuth authorization
          window.location.href = hubspotServer.oauthUrl;
          ```
          
          ```bash cURL
          # Copy and paste the OAuth URL into your browser
          echo "Visit this URL to authorize: https://api.klavis.ai/oauth/hubspot/authorize?instance_id=YOUR_INSTANCE_ID"
          ```
        </CodeGroup>
        
        <Check>
          🎉 **Your HubSpot MCP Server is ready!** Once authentication is complete, you can use your MCP server URL with any MCP-compatible client.
        </Check>
      </Step>
      

    </Steps>
  </Tab>
  
  <Tab title="UI">
    <Steps>
      <Step title="Open Dashboard">
        Go to your [Dashboard](https://www.klavis.ai/home/<USER>
      </Step>
      
      <Step title="Choose HubSpot">
        Select HubSpot from the list of available integrations.
      </Step>
      
      <Step title="Authenticate">
        Complete the OAuth flow to connect your HubSpot account.
      </Step>
      
      <Step title="Use in your app">
        Copy the MCP endpoint URL and add it to your MCP-supported client (Claude Desktop, Cursor, VS Code, etc.).
      </Step>
    </Steps>
  </Tab>
  
  <Tab title="Open Source">
    <Steps>
      <Step title="Clone repository (for developers to build from source)">
        ```bash
        git clone https://github.com/klavis-ai/klavis
        cd klavis/mcp_servers/hubspot
        ```
      </Step>
      
      <Step title="Run with Docker">
        ```bash
        # Pull the Docker image
        docker pull ghcr.io/klavis-ai/hubspot-mcp-server:latest
        
        # Run with OAuth support (requires Klavis API key)
        docker run -p 5000:5000 \
          -e KLAVIS_API_KEY=$KLAVIS_API_KEY \
          ghcr.io/klavis-ai/hubspot-mcp-server:latest
        ```
      </Step>
      
      <Step title="Configure MCP client">
        ```json
        {
          "mcpServers": {
            "hubspot": {
              "url": "http://localhost:5000/mcp/"
            }
          }
        }
        ```
      </Step>
    </Steps>
  </Tab>
</Tabs>

## Available Tools

<Accordion title="Tools Information">
| Tool Name | Description |
|-----------|-------------|
| hubspot_list_properties | List all property metadata for a HubSpot object type like contacts, companies, deals, or tickets |
| hubspot_search_by_property | Search HubSpot objects by a specific property and value using a filter operator |
| hubspot_get_contacts | Fetch a list of contacts from HubSpot |
| hubspot_get_contact_by_id | Get a specific contact by HubSpot contact ID |
| hubspot_create_property | Create a new custom property for HubSpot contacts |
| hubspot_delete_contact_by_id | Delete a contact from HubSpot by contact ID |
| hubspot_create_contact | Create a new contact using a JSON string of properties |
| hubspot_update_contact_by_id | Update a contact in HubSpot by contact ID using JSON property updates |
| hubspot_create_companies | Create a new company using a JSON string of fields |
| hubspot_get_companies | Fetch a list of companies from HubSpot |
| hubspot_get_company_by_id | Get a company from HubSpot by company ID |
| hubspot_update_company_by_id | Update an existing company by ID using JSON property updates |
| hubspot_delete_company_by_id | Delete a company from HubSpot by company ID |
| hubspot_get_deals | Fetch a list of deals from HubSpot |
| hubspot_get_deal_by_id | Fetch a deal by its ID |
| hubspot_create_deal | Create a new deal using a JSON string of properties |
| hubspot_update_deal_by_id | Update an existing deal using a JSON string of updated properties |
| hubspot_delete_deal_by_id | Delete a deal from HubSpot by deal ID |
| hubspot_get_tickets | Fetch a list of tickets from HubSpot |
| hubspot_get_ticket_by_id | Fetch a ticket by its ID |
| hubspot_create_ticket | Create a new ticket using a JSON string of properties |
| hubspot_update_ticket_by_id | Update an existing ticket using a JSON string of updated properties |
| hubspot_delete_ticket_by_id | Delete a ticket from HubSpot by ticket ID |
| hubspot_create_note | Create a new note in HubSpot with optional associations to contacts, companies, deals, or tickets |
</Accordion>

<Note>For more details about tool input schema, use the [get_tools](https://docs.klavis.ai/api-reference/mcp-server/get-tools) API.</Note>

## Next Steps

<CardGroup>
  <Card
    title="White-label Authentication"
    icon="palette"
    href="/documentation/auth/white-label"
  >
    Customize OAuth flows with your own branding
  </Card>
  
  <Card
    title="AI Platform Integrations"
    icon="robot"
    href="/documentation/ai-platform-integration/overview"
  >
    Integrate Klavis MCP Servers with leading AI platforms
  </Card>
  
  <Card
    title="Integrations"
    icon="puzzle-piece"
    href="/documentation/mcp-server/overview"
  >
    Explore available MCP servers
  </Card>
  
  <Card
    title="API Reference"
    icon="code"
    href="/api-reference/introduction"
  >
    REST endpoints and schemas
  </Card>
</CardGroup>