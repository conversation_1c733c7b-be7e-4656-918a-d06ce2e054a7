---
title: 'Figma'
description: 'Connect AI agents to Figma for design collaboration and asset management'
---

<Info>
  **Prerequisites**
  Before you begin, [create an account](https://www.klavis.ai/home/<USER>
</Info>

## Getting started

Connect to Figma to access design files, manage assets, retrieve comments, and collaborate on design projects through AI agents.

<Tabs>
  <Tab title="API">
    <Steps>
      <Step title="Install the SDKs (optional)">
        <CodeGroup>
          ```bash pip
          pip install klavis
          ```
          ```bash npm
          npm install klavis
          ```
        </CodeGroup>
      </Step>
      
      <Step title="Create a server instance">
        <CodeGroup>
          ```python Python
          from klavis import Klavis
          from klavis.types import McpServerName
          
          klavis_client = Klavis(api_key="YOUR_API_KEY")
          
          # Create a Figma MCP server instance
          figma_server = klavis_client.mcp_server.create_server_instance(
              server_name=McpServerName.FIGMA,
              user_id="user123"
          )
          ```
          
          ```typescript TypeScript
          import { KlavisC<PERSON>, Klavis } from 'klavis';
          
          const klavis = new KlavisClient({ apiKey: 'YOUR_API_KEY' });
          
          // Create a Figma MCP server instance
          const figmaServer = await klavis.mcpServer.createServerInstance({
              serverName: Klavis.McpServerName.Figma,
              userId: "user123"
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/create" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "serverName": "Figma",
              "userId": "user123"
            }'
          ```
        </CodeGroup>
        
        <Info>
          **Response Information**: The API returns:
          - `serverUrl`: The URL for connecting your MCP client to Figma
          - `instanceId`: Unique identifier for your server instance
        </Info>
      </Step>
      
      <Step title="Configure API Key">
        <CodeGroup>
          ```python Python
          # Set the Figma API key for your instance
          response = klavis_client.mcp_server.set_instance_auth(
              instance_id=figma_server.instance_id,
              auth_data={
                  "api_key": "YOUR_FIGMA_API_KEY"
              }
          )
          ```
          
          ```typescript TypeScript
          // Set the Figma API key for your instance
          const response = await klavis.mcpServer.setInstanceAuth({
              instanceId: figmaServer.instanceId,
              authData: {
                  api_key: "YOUR_FIGMA_API_KEY"
              }
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/set-auth" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "instanceId": "YOUR_INSTANCE_ID",
              "authData": {
                "api_key": "YOUR_FIGMA_API_KEY"
              }
            }'
          ```
        </CodeGroup>
        
        <Check>
          🎉 **Your Figma MCP Server is ready!** You can now use your MCP server URL with any MCP-compatible client.
        </Check>
      </Step>
      

    </Steps>
  </Tab>
  
  <Tab title="UI">
    <Steps>
      <Step title="Open Dashboard">
        Go to your [Dashboard](https://www.klavis.ai/home/<USER>
      </Step>
      
      <Step title="Choose Figma">
        Select Figma from the list of available integrations.
      </Step>
      
      <Step title="Configure API Key">
        Enter your Figma API key to authenticate the connection.
      </Step>
      
      <Step title="Use in your app">
        Copy the MCP endpoint URL and add it to your MCP-supported client (Claude Desktop, Cursor, VS Code, etc.).
      </Step>
    </Steps>
  </Tab>
  
  <Tab title="Open Source">
    <Steps>
      <Step title="Clone repository (for developers to build from source)">
        ```bash
        git clone https://github.com/klavis-ai/klavis
        cd klavis/mcp_servers/figma
        ```
      </Step>
      
      <Step title="Run with Docker">
        ```bash
        # Pull the Docker image
        docker pull ghcr.io/klavis-ai/figma-mcp-server:latest
        
        # Run with API key
        docker run -p 5000:5000 \
          -e AUTH_DATA='{"api_key":"your_figma_api_key"}' \
          ghcr.io/klavis-ai/figma-mcp-server:latest
        ```
      </Step>
      
      <Step title="Configure MCP client">
        ```json
        {
          "mcpServers": {
            "figma": {
              "url": "http://localhost:5000/mcp/"
            }
          }
        }
        ```
      </Step>
    </Steps>
  </Tab>
</Tabs>

## Available Tools

<Accordion title="Tools Information">
| Tool Name                   | Description                                                              |
|-----------------------------|--------------------------------------------------------------------------|
| get_file_content            | Retrieve content and data from a specific Figma file                     |
| list_files                  | List accessible Figma files in a project or team                         |
| create_component            | Create a new component in a Figma file                                   |
| export_designs              | Export designs or assets from a Figma file                               |
| get_comments                | Retrieve comments from a Figma file                                      |
</Accordion>

<Note>For more details about tool input schema, use the [get_tools](https://docs.klavis.ai/api-reference/mcp-server/get-tools) API.</Note>

## Next Steps

<CardGroup>
  <Card
    title="AI Platform Integrations"
    icon="robot"
    href="/documentation/ai-platform-integration/overview"
  >
    Integrate Klavis MCP Servers with leading AI platforms
  </Card>
  
  <Card
    title="Integrations"
    icon="puzzle-piece"
    href="/documentation/mcp-server/overview"
  >
    Explore available MCP servers
  </Card>
  
  <Card
    title="API Reference"
    icon="code"
    href="/api-reference/introduction"
  >
    REST endpoints and schemas
  </Card>
</CardGroup>