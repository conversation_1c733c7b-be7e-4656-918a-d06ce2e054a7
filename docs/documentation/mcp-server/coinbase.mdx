---
title: 'Coinbase'
description: 'Connect AI agents to Coinbase for cryptocurrency data and portfolio management'
---

<Info>
  **Prerequisites**
  Before you begin, [create an account](https://www.klavis.ai/home/<USER>
</Info>

## Getting started

Connect to Coinbase to access cryptocurrency prices, manage portfolios, and track transactions through AI agents.

<Tabs>
  <Tab title="API">
    <Steps>
      <Step title="Install the SDKs (optional)">
        <CodeGroup>
          ```bash pip
          pip install klavis
          ```
          ```bash npm
          npm install klavis
          ```
        </CodeGroup>
      </Step>
      
      <Step title="Create a server instance">
        <CodeGroup>
          ```python Python
          from klavis import Klavis
          from klavis.types import McpServerName
          
          klavis_client = Klavis(api_key="YOUR_API_KEY")
          
          # Create a Coinbase MCP server instance
          coinbase_server = klavis_client.mcp_server.create_server_instance(
              server_name=McpServerName.COINBASE,
              user_id="user123"
          )
          ```
          
          ```typescript TypeScript
          import { KlavisClient, Klavis } from 'klavis';
          
          const klavis = new KlavisClient({ apiKey: 'YOUR_API_KEY' });
          
          // Create a Coinbase MCP server instance
          const coinbaseServer = await klavis.mcpServer.createServerInstance({
              serverName: Klavis.McpServerName.Coinbase,
              userId: "user123"
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/create" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "serverName": "Coinbase",
              "userId": "user123"
            }'
          ```
        </CodeGroup>
        
        <Info>
          **Response Information**: The API returns:
          - `serverUrl`: The URL for connecting your MCP client to Coinbase
          - `instanceId`: Unique identifier for your server instance
          - Note: Coinbase requires API credentials which should be configured after creation
        </Info>
      </Step>
      
      <Step title="Configure authentication">
        <CodeGroup>
          ```python Python
          # Set the Coinbase API credentials for your instance
          response = klavis_client.mcp_server.set_instance_auth(
              instance_id=coinbase_server.instance_id,
              auth_data={
                  "api_key": "YOUR_COINBASE_API_KEY",
                  "secret_key": "YOUR_COINBASE_API_SECRET"
              }
          )
          ```
          
          ```typescript TypeScript
          // Set the Coinbase API credentials for your instance
          const response = await klavis.mcpServer.setInstanceAuth({
              instanceId: coinbaseServer.instanceId,
              authData: {
                  api_key: "YOUR_COINBASE_API_KEY",
                  secret_key: "YOUR_COINBASE_API_SECRET"
              }
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/set-auth" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "instanceId": "YOUR_INSTANCE_ID",
              "authData": {
                "api_key": "YOUR_COINBASE_API_KEY",
                "secret_key": "YOUR_COINBASE_API_SECRET"
              }
            }'
          ```
        </CodeGroup>
        
        <Check>
          🎉 **Your Coinbase MCP Server is ready!** You can now use your MCP server URL with any MCP-compatible client to access cryptocurrency data.
        </Check>
      </Step>
      

    </Steps>
  </Tab>
  
  <Tab title="UI">
    <Steps>
      <Step title="Open Dashboard">
        Go to your [Dashboard](https://www.klavis.ai/home/<USER>
      </Step>
      
      <Step title="Choose Coinbase">
        Select Coinbase from the list of available integrations.
      </Step>
      
      <Step title="Configure credentials">
        Enter your Coinbase API key and secret to enable cryptocurrency data access.
      </Step>
      
      <Step title="Use in your app">
        Copy the MCP endpoint URL and add it to your MCP-supported client (Claude Desktop, Cursor, VS Code, etc.).
      </Step>
    </Steps>
  </Tab>
  
  <Tab title="Open Source">
    <Steps>
      <Step title="Clone repository (for developers to build from source)">
        ```bash
        git clone https://github.com/klavis-ai/klavis
        cd klavis/mcp_servers/coinbase
        ```
      </Step>
      
      <Step title="Run with Docker">
        ```bash
        # Pull the Docker image
        docker pull ghcr.io/klavis-ai/coinbase-mcp-server:latest
        
        # Run with Coinbase API credentials
        docker run -p 5000:5000 \
          -e AUTH_DATA='{"api_key":"your_coinbase_api_key","secret_key":"your_coinbase_secret"}' \
          ghcr.io/klavis-ai/coinbase-mcp-server:latest
        ```
      </Step>
      
      <Step title="Configure MCP client">
        ```json
        {
          "mcpServers": {
            "coinbase": {
              "url": "http://localhost:5000/mcp/"
            }
          }
        }
        ```
      </Step>
    </Steps>
  </Tab>
</Tabs>

## Available Tools

<Accordion title="Tools Information">
| Tool Name | Description |
|-----------|-------------|
| coinbase_get_prices | Get current prices of cryptocurrency from Coinbase |
| coinbase_get_current_exchange_rate | Get current exchange rate of cryptocurrency from Coinbase |
| coinbase_get_historical_prices | Get historical price data of cryptocurrency from Coinbase |
| coinbase_get_accounts | List user's Coinbase accounts |
| coinbase_get_account_balance | Get balance for user's account |
| coinbase_get_transactions | Get transaction history for an account |
| coinbase_get_portfolio_value | Get portfolio value for user's accounts |
| coinbase_get_product_details | Get product details of a cryptocurrency from Coinbase |
</Accordion>

<Note>For more details about tool input schema, use the [get_tools](https://docs.klavis.ai/api-reference/mcp-server/get-tools) API.</Note>

## Next Steps

<CardGroup>
  <Card
    title="AI Platform Integrations"
    icon="robot"
    href="/documentation/ai-platform-integration/overview"
  >
    Integrate Klavis MCP Servers with leading AI platforms
  </Card>
  
  <Card
    title="Integrations"
    icon="puzzle-piece"
    href="/documentation/mcp-server/overview"
  >
    Explore available MCP servers
  </Card>
  
  <Card
    title="API Reference"
    icon="code"
    href="/api-reference/introduction"
  >
    REST endpoints and schemas
  </Card>
</CardGroup>
