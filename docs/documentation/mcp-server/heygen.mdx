---
title: 'HeyGen'
description: 'Connect AI agents to HeyGen for AI-powered video generation and avatar creation'
---

<Info>
  **Prerequisites**
  Before you begin, [create an account](https://www.klavis.ai/home/<USER>
</Info>

## Getting started

Connect to HeyGen to generate AI-powered videos with avatars, manage video content, and create personalized video experiences through AI agents.

<Tabs>
  <Tab title="API">
    <Steps>
      <Step title="Install the SDKs (optional)">
        <CodeGroup>
          ```bash pip
          pip install klavis
          ```
          ```bash npm
          npm install klavis
          ```
        </CodeGroup>
      </Step>
      
      <Step title="Create a server instance">
        <CodeGroup>
          ```python Python
          from klavis import Klavis
          from klavis.types import McpServerName
          
          klavis_client = Klavis(api_key="YOUR_API_KEY")
          
          # Create a HeyGen MCP server instance
          heygen_server = klavis_client.mcp_server.create_server_instance(
              server_name=McpServerName.HEYGEN,
              user_id="user123"
          )
          ```
          
          ```typescript TypeScript
          import { KlavisClient, Klavis } from 'klavis';
          
          const klavis = new KlavisClient({ apiKey: 'YOUR_API_KEY' });
          
          // Create a HeyGen MCP server instance
          const heygenServer = await klavis.mcpServer.createServerInstance({
              serverName: Klavis.McpServerName.HeyGen,
              userId: "user123"
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/create" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "serverName": "HeyGen",
              "userId": "user123"
            }'
          ```
        </CodeGroup>
        
        <Info>
          **Response Information**: The API returns:
          - `serverUrl`: The URL for connecting your MCP client to HeyGen
          - `instanceId`: Unique identifier for your server instance
        </Info>
      </Step>
      
      <Step title="Configure API Key">
        <CodeGroup>
          ```python Python
          # Set the HeyGen API key for your instance
          response = klavis_client.mcp_server.set_instance_auth(
              instance_id=heygen_server.instance_id,
              auth_data={
                  "token": "YOUR_HEYGEN_API_KEY"
              }
          )
          ```
          
          ```typescript TypeScript
          // Set the HeyGen API key for your instance
          const response = await klavis.mcpServer.setInstanceAuth({
              instanceId: heygenServer.instanceId,
              authData: {
                  token: "YOUR_HEYGEN_API_KEY"
              }
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/set-auth" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "instanceId": "YOUR_INSTANCE_ID",
              "authData": {
                "token": "YOUR_HEYGEN_API_KEY"
              }
            }'
          ```
        </CodeGroup>
        
        <Check>
          🎉 **Your HeyGen MCP Server is ready!** You can now use your MCP server URL with any MCP-compatible client.
        </Check>
      </Step>
      

    </Steps>
  </Tab>
  
  <Tab title="UI">
    <Steps>
      <Step title="Open Dashboard">
        Go to your [Dashboard](https://www.klavis.ai/home/<USER>
      </Step>
      
      <Step title="Choose HeyGen">
        Select HeyGen from the list of available integrations.
      </Step>
      
      <Step title="Configure API Key">
        Enter your HeyGen API key to authenticate the connection.
      </Step>
      
      <Step title="Use in your app">
        Copy the MCP endpoint URL and add it to your MCP-supported client (Claude Desktop, Cursor, VS Code, etc.).
      </Step>
    </Steps>
  </Tab>
  
  <Tab title="Open Source">
    <Steps>
      <Step title="Clone repository (for developers to build from source)">
        ```bash
        git clone https://github.com/klavis-ai/klavis
        cd klavis/mcp_servers/heygen
        ```
      </Step>
      
      <Step title="Run with Docker">
        ```bash
        # Pull the Docker image
        docker pull ghcr.io/klavis-ai/heygen-mcp-server:latest
        
        # Run with API key
        docker run -p 5000:5000 \
          -e AUTH_DATA='{"token":"your_heygen_api_key"}' \
          ghcr.io/klavis-ai/heygen-mcp-server:latest
        ```
      </Step>
      
      <Step title="Configure MCP client">
        ```json
        {
          "mcpServers": {
            "heygen": {
              "url": "http://localhost:5000/mcp/"
            }
          }
        }
        ```
      </Step>
    </Steps>
  </Tab>
</Tabs>

## Available Tools

<Accordion title="Tools Information">
| Tool Name   | Description                                                              |
|-------------|--------------------------------------------------------------------------|
| heygen_get_remaining_credits | Retrieve the remaining credits in your HeyGen account |
| heygen_get_voices | Retrieve a list of available voices from the HeyGen API |
| heygen_get_voice_locales | Retrieve a list of available voice locales (languages) from the HeyGen API |
| heygen_get_avatar_groups | Retrieve a list of HeyGen avatar groups |
| heygen_get_avatars_in_avatar_group | Retrieve a list of avatars in a specific HeyGen avatar group |
| heygen_list_avatars | Retrieve a list of all available avatars from the HeyGen API |
| heygen_generate_avatar_video | Generate a new avatar video with the specified avatar, text, and voice |
| heygen_get_avatar_video_status | Retrieve the status of a video generated via the HeyGen API |
| heygen_list_videos | Retrieve a list of videos from your HeyGen account |
| heygen_delete_video | Delete a video from your HeyGen account |
</Accordion>

<Note>For more details about tool input schema, use the [get_tools](https://docs.klavis.ai/api-reference/mcp-server/get-tools) API.</Note>

## Next Steps

<CardGroup>
  <Card
    title="AI Platform Integrations"
    icon="robot"
    href="/documentation/ai-platform-integration/overview"
  >
    Integrate Klavis MCP Servers with leading AI platforms
  </Card>
  
  <Card
    title="Integrations"
    icon="puzzle-piece"
    href="/documentation/mcp-server/overview"
  >
    Explore available MCP servers
  </Card>
  
  <Card
    title="API Reference"
    icon="code"
    href="/api-reference/introduction"
  >
    REST endpoints and schemas
  </Card>
</CardGroup>