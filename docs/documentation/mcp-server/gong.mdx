---
title: '<PERSON>'
description: 'Connect AI agents to Gong for sales conversation intelligence and call analytics'
---

<Info>
  **Prerequisites**
  Before you begin, [create an account](https://www.klavis.ai/home/<USER>
</Info>

## Getting started

Connect to <PERSON> to access sales conversation intelligence, analyze call transcripts, and extract insights from sales meetings through AI agents.

<Tabs>
  <Tab title="API">
    <Steps>
      <Step title="Install the SDKs (optional)">
        <CodeGroup>
          ```bash pip
          pip install klavis
          ```
          ```bash npm
          npm install klavis
          ```
        </CodeGroup>
      </Step>
      
      <Step title="Create a server instance">
        <CodeGroup>
          ```python Python
          from klavis import Klavis
          from klavis.types import McpServerName
          
          klavis_client = Klavis(api_key="YOUR_API_KEY")
          
          # Create a Gong MCP server instance
          gong_server = klavis_client.mcp_server.create_server_instance(
              server_name=McpServerName.GONG,
              user_id="user123"
          )
          ```
          
          ```typescript TypeScript
          import { KlavisClient, Klavis } from 'klavis';
          
          const klavis = new KlavisClient({ apiKey: 'YOUR_API_KEY' });
          
          // Create a Gong MCP server instance
          const gongServer = await klavis.mcpServer.createServerInstance({
              serverName: Klavis.McpServerName.Gong,
              userId: "user123"
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/create" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "serverName": "Gong",
              "userId": "user123"
            }'
          ```
        </CodeGroup>
        
        <Info>
          **Response Information**: The API returns:
          - `serverUrl`: The URL for connecting your MCP client to Gong
          - `instanceId`: Unique identifier for your server instance
          - Note: Gong requires an API key which should be configured after creation
        </Info>
      </Step>
      
      <Step title="Configure authentication">
        <Info>
          You can get your Gong API key from your Gong admin settings. Contact your Gong administrator for access.
        </Info>
        
        <CodeGroup>
          ```python Python
          # Set the Gong API key for your instance
          response = klavis_client.mcp_server.set_instance_auth(
              instance_id=gong_server.instance_id,
              auth_data={
                  "token": "YOUR_GONG_API_KEY"
              }
          )
          ```
          
          ```typescript TypeScript
          // Set the Gong API key for your instance
          const response = await klavis.mcpServer.setInstanceAuth({
              instanceId: gongServer.instanceId,
              authData: {
                  token: "YOUR_GONG_API_KEY"
              }
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/set-auth" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "instanceId": "YOUR_INSTANCE_ID",
              "authData": {
                "token": "YOUR_GONG_API_KEY"
              }
            }'
          ```
        </CodeGroup>
        
        <Check>
          🎉 **Your Gong MCP Server is ready!** You can now use your MCP server URL with any MCP-compatible client to analyze sales conversations.
        </Check>
      </Step>
      

    </Steps>
  </Tab>
  
  <Tab title="UI">
    <Steps>
      <Step title="Open Dashboard">
        Go to your [Dashboard](https://www.klavis.ai/home/<USER>
      </Step>
      
      <Step title="Choose Gong">
        Select Gong from the list of available integrations.
      </Step>
      
      <Step title="Configure API Key">
        Enter your Gong API key to enable conversation intelligence access.
      </Step>
      
      <Step title="Use in your app">
        Copy the MCP endpoint URL and add it to your MCP-supported client (Claude Desktop, Cursor, VS Code, etc.).
      </Step>
    </Steps>
  </Tab>
  
  <Tab title="Open Source">
    <Steps>
      <Step title="Clone repository (for developers to build from source)">
        ```bash
        git clone https://github.com/klavis-ai/klavis
        cd klavis/mcp_servers/gong
        ```
      </Step>
      
      <Step title="Run with Docker">
        ```bash
        # Pull the Docker image
        docker pull ghcr.io/klavis-ai/gong-mcp-server:latest
        
        # Run with Gong API key
        docker run -p 5000:5000 \
          -e AUTH_DATA='{"token":"your_gong_api_key"}' \
          ghcr.io/klavis-ai/gong-mcp-server:latest
        ```
      </Step>
      
      <Step title="Configure MCP client">
        ```json
        {
          "mcpServers": {
            "gong": {
              "url": "http://localhost:5000/mcp/"
            }
          }
        }
        ```
      </Step>
    </Steps>
  </Tab>
</Tabs>

## Available Tools

<Accordion title="Tools Information">
| Tool Name | Description |
|-----------|-------------|
| gong_get_transcripts_by_user | Get call transcripts associated with a user by email address including all participants on the call and their companies |
| gong_get_extensive_data | Lists detailed call data for specific call IDs |
| gong_get_call_transcripts | Retrieve transcripts of specific calls |
| gong_list_calls | List calls within a date range |
| gong_add_new_call | Add a new call record to Gong |
</Accordion>

<Note>For more details about tool input schema, use the [get_tools](https://docs.klavis.ai/api-reference/mcp-server/get-tools) API.</Note>

## Next Steps

<CardGroup>
  <Card
    title="AI Platform Integrations"
    icon="robot"
    href="/documentation/ai-platform-integration/overview"
  >
    Integrate Klavis MCP Servers with leading AI platforms
  </Card>
  
  <Card
    title="Integrations"
    icon="puzzle-piece"
    href="/documentation/mcp-server/overview"
  >
    Explore available MCP servers
  </Card>
  
  <Card
    title="API Reference"
    icon="code"
    href="/api-reference/introduction"
  >
    REST endpoints and schemas
  </Card>
</CardGroup> 