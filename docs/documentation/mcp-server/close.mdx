---
title: 'Close'
description: 'Connect AI agents to Close CRM for sales pipeline management and lead automation'
---

<Info>
  **Prerequisites**
  Before you begin, [create an account](https://www.klavis.ai/home/<USER>
</Info>

## Getting started

Connect to Close CRM to manage leads, contacts, opportunities, and automate sales workflows through AI agents.

<Tabs>
  <Tab title="API">
    <Steps>
      <Step title="Install the SDKs (optional)">
        <CodeGroup>
          ```bash pip
          pip install klavis
          ```
          ```bash npm
          npm install klavis
          ```
        </CodeGroup>
      </Step>
      
      <Step title="Create a server instance">
        <CodeGroup>
          ```python Python
          from klavis import Klavis
          from klavis.types import McpServerName
          
          klavis_client = Klavis(api_key="YOUR_API_KEY")
          
          # Create a Close CRM MCP server instance
          close_server = klavis_client.mcp_server.create_server_instance(
              server_name=McpServerName.CLOSE,
              user_id="user123"
          )
          ```
          
          ```typescript TypeScript
          import { KlavisC<PERSON>, Klavis } from 'klavis';
          
          const klavis = new KlavisClient({ apiKey: 'YOUR_API_KEY' });
          
          // Create a Close CRM MCP server instance
          const closeServer = await klavis.mcpServer.createServerInstance({
              serverName: Klavis.McpServerName.Close,
              userId: "user123"
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/create" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "serverName": "Close",
              "userId": "user123"
            }'
          ```
        </CodeGroup>
        
        <Info>
          **Response Information**: The API returns:
          - `serverUrl`: The URL for connecting your MCP client to Close CRM
          - `instanceId`: Unique identifier for your server instance
          - `oauthUrl`: OAuth authorization URL for Close CRM authentication
        </Info>
      </Step>
      
      <Step title="Authenticate">
        <CodeGroup>
          ```python Python
          import webbrowser
          
          # Open OAuth authorization page
          webbrowser.open(close_server.oauth_url)
          ```
          
          ```typescript TypeScript
          // Redirect user to OAuth authorization
          window.location.href = closeServer.oauthUrl;
          ```
          
          ```bash cURL
          # Copy and paste the OAuth URL into your browser
          echo "Visit this URL to authorize: https://api.klavis.ai/oauth/close/authorize?instance_id=YOUR_INSTANCE_ID"
          ```
        </CodeGroup>
        
        <Check>
          🎉 **Your Close CRM MCP Server is ready!** Once authentication is complete, you can use your MCP server URL with any MCP-compatible client.
        </Check>
      </Step>
      

    </Steps>
  </Tab>
  
  <Tab title="UI">
    <Steps>
      <Step title="Open Dashboard">
        Go to your [Dashboard](https://www.klavis.ai/home/<USER>
      </Step>
      
      <Step title="Choose Close">
        Select Close from the list of available integrations.
      </Step>
      
      <Step title="Authenticate">
        Complete the OAuth flow to connect your Close CRM account.
      </Step>
      
      <Step title="Use in your app">
        Copy the MCP endpoint URL and add it to your MCP-supported client (Claude Desktop, Cursor, VS Code, etc.).
      </Step>
    </Steps>
  </Tab>
  
  <Tab title="Open Source">
    <Steps>
      <Step title="Clone repository (for developers to build from source)">
        ```bash
        git clone https://github.com/klavis-ai/klavis
        cd klavis/mcp_servers/close
        ```
      </Step>
      
      <Step title="Run with Docker">
        ```bash
        # Pull the Docker image
        docker pull ghcr.io/klavis-ai/close-mcp-server:latest
        
        # Run with OAuth support (requires Klavis API key)
        docker run -p 5000:5000 \
          -e KLAVIS_API_KEY=$KLAVIS_API_KEY \
          ghcr.io/klavis-ai/close-mcp-server:latest
        ```
      </Step>
      
      <Step title="Configure MCP client">
        ```json
        {
          "mcpServers": {
            "close": {
              "url": "http://localhost:5000/mcp/"
            }
          }
        }
        ```
      </Step>
    </Steps>
  </Tab>
</Tabs>

## Available Tools

<Accordion title="Tools Information">
| Tool Name | Description |
|-----------|-------------|
| close_create_lead | Create a new lead in Close CRM |
| close_get_lead | Get a lead by its ID from Close CRM |
| close_search_leads | Search for leads in Close CRM |
| close_update_lead | Update an existing lead in Close CRM |
| close_delete_lead | Delete a lead from Close CRM |
| close_list_leads | List leads from Close CRM |
| close_create_contact | Create a new contact in Close CRM |
| close_get_contact | Get a contact by its ID from Close CRM |
| close_search_contacts | Search for contacts in Close CRM |
| close_update_contact | Update an existing contact in Close CRM |
| close_delete_contact | Delete a contact from Close CRM |
| close_create_opportunity | Create a new opportunity in Close CRM |
| close_get_opportunity | Get an opportunity by its ID from Close CRM |
| close_update_opportunity | Update an existing opportunity in Close CRM |
| close_delete_opportunity | Delete an opportunity from Close CRM |
| close_create_task | Create a new task in Close CRM |
| close_get_task | Get a task by its ID from Close CRM |
| close_update_task | Update an existing task in Close CRM |
| close_delete_task | Delete a task from Close CRM |
| close_list_tasks | List tasks from Close CRM |
| close_get_current_user | Get information about the current user |
| close_list_users | List users from Close CRM |
| close_get_user | Get a user by their ID from Close CRM |
</Accordion>

<Note>For more details about tool input schema, use the [get_tools](https://docs.klavis.ai/api-reference/mcp-server/get-tools) API.</Note>

## Next Steps

<CardGroup>
  <Card
    title="White-label Authentication"
    icon="palette"
    href="/documentation/auth/white-label"
  >
    Customize OAuth flows with your own branding
  </Card>
  
  <Card
    title="AI Platform Integrations"
    icon="robot"
    href="/documentation/ai-platform-integration/overview"
  >
    Integrate Klavis MCP Servers with leading AI platforms
  </Card>
  
  <Card
    title="Integrations"
    icon="puzzle-piece"
    href="/documentation/mcp-server/overview"
  >
    Explore available MCP servers
  </Card>
  
  <Card
    title="API Reference"
    icon="code"
    href="/api-reference/introduction"
  >
    REST endpoints and schemas
  </Card>
</CardGroup>