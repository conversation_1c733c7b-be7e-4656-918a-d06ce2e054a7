---
title: 'Doc2markdown'
description: 'Connect AI agents to Doc2markdown for document format conversion'
---

<Info>
  **Prerequisites**
  Before you begin, [create an account](https://www.klavis.ai/home/<USER>
</Info>

## Getting started

Connect to Doc2markdown to convert various document formats (PDF, PowerPoint, Word, Excel, HTML, ZIP files, and EPubs) to markdown through AI agents.

<Tabs>
  <Tab title="API">
    <Steps>
      <Step title="Install the SDKs (optional)">
        <CodeGroup>
          ```bash pip
          pip install klavis
          ```
          ```bash npm
          npm install klavis
          ```
        </CodeGroup>
      </Step>
      
      <Step title="Create a server instance">
        <CodeGroup>
          ```python Python
          from klavis import Klavis
          from klavis.types import McpServerName
          
          klavis_client = Klavis(api_key="YOUR_API_KEY")
          
          # Create a Doc2markdown MCP server instance
          doc2markdown_server = klavis_client.mcp_server.create_server_instance(
              server_name=McpServerName.DOC2MARKDOWN,
              user_id="user123"
          )
          ```
          
          ```typescript TypeScript
          import { KlavisClient, Klavis } from 'klavis';
          
          const klavis = new KlavisClient({ apiKey: 'YOUR_API_KEY' });
          
          // Create a Doc2markdown MCP server instance
          const doc2markdownServer = await klavis.mcpServer.createServerInstance({
              serverName: Klavis.McpServerName.Doc2markdown,
              userId: "user123"
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/create" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "serverName": "Doc2markdown",
              "userId": "user123"
            }'
          ```
        </CodeGroup>
        
        <Info>
          **Response Information**: The API returns:
          - `serverUrl`: The URL for connecting your MCP client to Doc2markdown
          - `instanceId`: Unique identifier for your server instance
        </Info>
      </Step>
      
      <Step title="Configure API Key">
        <CodeGroup>
          ```python Python
          # Set the Doc2markdown API key for your instance
          response = klavis_client.mcp_server.set_instance_auth(
              instance_id=doc2markdown_server.instance_id,
              auth_data={
                  "api_key": "YOUR_DOC2MARKDOWN_API_KEY"
              }
          )
          ```
          
          ```typescript TypeScript
          // Set the Doc2markdown API key for your instance
          const response = await klavis.mcpServer.setInstanceAuth({
              instanceId: doc2markdownServer.instanceId,
              authData: {
                  api_key: "YOUR_DOC2MARKDOWN_API_KEY"
              }
          });
          ```
          
          ```bash cURL
          curl -X POST "https://api.klavis.ai/mcp-server/instance/set-auth" \
            -H "Authorization: Bearer YOUR_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "instanceId": "YOUR_INSTANCE_ID",
              "authData": {
                "api_key": "YOUR_DOC2MARKDOWN_API_KEY"
              }
            }'
          ```
        </CodeGroup>
        
        <Check>
          🎉 **Your Doc2markdown MCP Server is ready!** You can now use your MCP server URL with any MCP-compatible client.
        </Check>
      </Step>
      

    </Steps>
  </Tab>
  
  <Tab title="UI">
    <Steps>
      <Step title="Open Dashboard">
        Go to your [Dashboard](https://www.klavis.ai/home/<USER>
      </Step>
      
      <Step title="Choose Doc2markdown">
        Select Doc2markdown from the list of available integrations.
      </Step>
      
      <Step title="Configure API Key">
        Enter your Doc2markdown API key to authenticate the connection.
      </Step>
      
      <Step title="Use in your app">
        Copy the MCP endpoint URL and add it to your MCP-supported client (Claude Desktop, Cursor, VS Code, etc.).
      </Step>
    </Steps>
  </Tab>
  
  <Tab title="Open Source">
    <Steps>
      <Step title="Clone repository (for developers to build from source)">
        ```bash
        git clone https://github.com/klavis-ai/klavis
        cd klavis/mcp_servers/doc2markdown
        ```
      </Step>
      
      <Step title="Run with Docker">
        ```bash
        # Pull the Docker image
        docker pull ghcr.io/klavis-ai/doc2markdown-mcp-server:latest
        
        # Run with API key
        docker run -p 5000:5000 \
          -e AUTH_DATA='{"api_key":"your_doc2markdown_api_key"}' \
          ghcr.io/klavis-ai/doc2markdown-mcp-server:latest
        ```
      </Step>
      
      <Step title="Configure MCP client">
        ```json
        {
          "mcpServers": {
            "doc2markdown": {
              "url": "http://localhost:5000/mcp/"
            }
          }
        }
        ```
      </Step>
    </Steps>
  </Tab>
</Tabs>

## Available Tools

<Accordion title="Tools Information">
| Tool Name                     | Description                                                              |
|-------------------------------|--------------------------------------------------------------------------|
| convert_document_to_markdown  | Convert a document from a URI to markdown. Supports PDF, PowerPoint, Word, Excel, HTML, text-based formats, ZIP files, and EPubs |
</Accordion>

<Note>For more details about tool input schema, use the [get_tools](https://docs.klavis.ai/api-reference/mcp-server/get-tools) API.</Note>

## Next Steps

<CardGroup>
  <Card
    title="AI Platform Integrations"
    icon="robot"
    href="/documentation/ai-platform-integration/overview"
  >
    Integrate Klavis MCP Servers with leading AI platforms
  </Card>
  
  <Card
    title="Integrations"
    icon="puzzle-piece"
    href="/documentation/mcp-server/overview"
  >
    Explore available MCP servers
  </Card>
  
  <Card
    title="API Reference"
    icon="code"
    href="/api-reference/introduction"
  >
    REST endpoints and schemas
  </Card>
</CardGroup>