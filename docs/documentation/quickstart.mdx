---
title: Quickstart
description: Connect any integration in minutes via MCP
icon: rocket
mode: "wide"
---

<Info>

**Prerequisites** Before you begin, [create an account](https://www.klavis.ai/home/<USER>

</Info>

## Getting started

<Card title="Multi-app integration (Strata)" icon="layer-group" horizontal href="#multi-app-integration">
  Optimized to handle tool overload and context window limits, ideal when connecting to multiple integrations. Click to jump >
</Card>

<Card title="Individual-app integration" icon="terminal" horizontal href="#individual-app-integration">
  Best suited for vertical AI agents that only need to connect to a limited set of tools or a single MCP Server. Click to jump >
</Card>

## Multi-app integration

One MCP server for AI agents to handle thousands of tools progressively.

<Tabs>
  <Tab title="UI">
    <Steps>
      <Step title="Open Dashboard">
        Go to your <a href="https://www.klavis.ai/home/<USER>">Dashboard</a>.
        
        <Tip>
        Klavis enables all integrations for you by default. Click the ellipsis button if you want to disable a specific integration.
        </Tip>
        
        <img
          className="block dark:hidden"
          src="/images/get-started/quickstart/strata_ui.png"
          alt="Strata UI Dashboard"
        />
        <img
          className="hidden dark:block"
          src="/images/get-started/quickstart/strata_ui.png"
          alt="Strata UI Dashboard"
        />
      </Step>
      <Step title="(Optional) Authenticate">
        Complete authentication by clicking the "Authorize" button. You can also skip this step since we have an authentication handler tool that will prompt to authenticate when needed.
      </Step>
      <Step title="Use in your app">
        Add to your favorite MCP-supported clients, such as Cursor, Claude Code, VS Code, ChatGPT, etc.
      </Step>
    </Steps>
  </Tab>
  <Tab title="API">
    <Steps>
      <Step title="Install the SDKs">
        <CodeGroup>
          ```bash pip
          pip install klavis
          ```

          ```bash npm
          npm install klavis
          ```
        </CodeGroup>
      </Step>
      <Step title="Create Strata MCP Server">
        <CodeGroup>
          ```bash Curl
          curl -X POST "https://api.klavis.ai/mcp-server/strata/create" \
            -H "Authorization: Bearer YOUR_KLAVIS_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "userId": "user123",
              "servers": ["Gmail", "YouTube"]
            }'
          ```

          ```python Python
          from klavis import Klavis
          from klavis.types import McpServerName

          klavis_client = Klavis(api_key="YOUR_KLAVIS_API_KEY")

          response = klavis_client.mcp_server.create_strata_server(
              user_id="user123",
              servers=[McpServerName.GMAIL, McpServerName.YOUTUBE],
          )
          ```

          ```typescript TypeScript
          import { Klavis } from 'klavis';

          const klavis = new Klavis.Client({ apiKey: 'YOUR_KLAVIS_API_KEY' });
          const strata = await klavis.strata.create({
            userId: 'user123',
            servers: ['GMAIL', 'YOUTUBE'],
          });
          ```
        </CodeGroup>
        <Info>
        **Response Information**: The API returns:
        - `strataServerUrl`: The URL you'll use to connect your MCP client to the Strata MCP Server
        - `oauthUrls`: Authorization links for services that require OAuth authentication
        - `apiKeyUrls`: Links to configure API keys for services that use API key authentication
        </Info>
        <Card title="API Reference" icon="magnifying-glass" href="/api-reference/strata/create" horizontal>
          Full Strata API endpoints
        </Card>
      </Step>
      <Step title="Authenticate required apps">
        <CodeGroup>
          ```bash Curl
          Copy and paste the OAuth URL into your web browser
          ```
          ```python Python
          import webbrowser

          # Handle OAuth authorization if needed
          if response.oauth_urls:
              for server_name, oauth_url in response.oauth_urls.items():
                  webbrowser.open(oauth_url)
                  input(f"Press Enter after completing {server_name} OAuth authorization...")
          ```

          ```typescript TypeScript
          // Handle OAuth authorization if needed
          if (response.oauthUrls) {
              for (const [serverName, oauthUrl] of Object.entries(response.oauthUrls)) {
                  if (typeof window !== 'undefined') {
                      window.open(oauthUrl);
                  }
                  console.log(`Please complete ${serverName} OAuth authorization at: ${oauthUrl}`);
                  // In a real application, you'd wait for OAuth completion via callback
                  await new Promise(resolve => {
                      console.log(`Press any key after completing ${serverName} OAuth authorization...`);
                      // This would be replaced with proper OAuth flow handling
                      resolve(null);
                  });
              }
          }
          ```
        </CodeGroup>
        
        <Info>
        **Authentication Methods**:
        - **API Key**: See [API Key authentication guide](/documentation/auth/api-key) for details.
        - **OAuth**: See [OAuth authentication guide](/documentation/auth/oauth) for details.
        </Info>
        
        <Check>
        🎉 **Your MCP Server URL is ready to use!** Once authentication is complete, you can use your MCP server URL with any MCP-compatible client.
        </Check>
      </Step>
      <Step title="(Optional) Connect to your AI application">
        <Tabs>
          <Tab title="LangChain">
            <CodeGroup>
              ```python Python
              import os
              import asyncio
              import webbrowser

              from klavis import Klavis
              from klavis.types import McpServerName
              from langchain_openai import ChatOpenAI
              from langchain_mcp_adapters.client import MultiServerMCPClient
              from langgraph.prebuilt import create_react_agent

              from dotenv import load_dotenv
              load_dotenv()

              async def main():
                  klavis_client = Klavis(api_key=os.getenv("KLAVIS_API_KEY"))

                  # Step 1: Create a Strata MCP server with Gmail and YouTube integrations
                  response = klavis_client.mcp_server.create_strata_server(
                      user_id="demo_user",
                      servers=[McpServerName.GMAIL, McpServerName.YOUTUBE],
                  )

                  # Step 2: Handle OAuth authorization if needed
                  if response.oauth_urls:
                      for server_name, oauth_url in response.oauth_urls.items():
                          webbrowser.open(oauth_url)
                          input(f"Press Enter after completing {server_name} OAuth authorization...")

                  # Step 3: Create LangChain Agent with MCP Tools
                  mcp_client = MultiServerMCPClient({
                      "strata": {
                          "transport": "streamable_http",
                          "url": response.strata_server_url,
                      }
                  })

                  # Get all available tools from Strata
                  tools = await mcp_client.get_tools()
                  # Setup LLM
                  llm = ChatOpenAI(model="gpt-4o-mini", api_key=os.getenv("OPENAI_API_KEY"))
                  
                  # Step 4: Create LangChain agent with MCP tools
                  agent = create_react_agent(
                      model=llm,
                      tools=tools,
                      prompt=(
                          "You are a helpful assistant that can use MCP tools. "
                      ),
                  )

                  my_email = "<EMAIL>" # TODO: Replace with your email
                  # Step 5: Invoke the agent
                  result = await agent.ainvoke({
                      "messages": [{"role": "user", "content": f"summarize this video - https://youtu.be/yebNIHKAC4A?si=1Rz_ZsiVRz0YfOR7 and send the summary to my email {my_email}"}],
                  })
                  
                  # Print only the final AI response content
                  print(result["messages"][-1].content)

              if __name__ == "__main__":
                  asyncio.run(main())
              ```
            </CodeGroup>
          </Tab>
          <Tab title="LlamaIndex">
           <CodeGroup>
              ```python Python
              import os
              import asyncio
              import webbrowser
              
              from klavis import Klavis
              from klavis.types import McpServerName
              from llama_index.llms.openai import OpenAI
              from llama_index.core.agent.workflow import FunctionAgent
              from llama_index.tools.mcp import BasicMCPClient
              from llama_index.tools.mcp import (
                  aget_tools_from_mcp_url,
              )
              
              from dotenv import load_dotenv
              load_dotenv()
              
              async def main():
                  klavis_client = Klavis(api_key=os.getenv("KLAVIS_API_KEY"))
              
                  # Step 1: Create a Strata MCP server with Gmail and YouTube integrations
                  response = klavis_client.mcp_server.create_strata_server(
                      user_id="1234",
                      servers=[McpServerName.GMAIL, McpServerName.YOUTUBE],
                  )
              
                  # Step 2: Handle OAuth authorization if needed
                  if response.oauth_urls:
                      for server_name, oauth_url in response.oauth_urls.items():
                          webbrowser.open(oauth_url)
                          input(f"Press Enter after completing {server_name} OAuth authorization...")
              
                  # Get all available tools from Strata
                  tools = await aget_tools_from_mcp_url(
                      response.strata_server_url, 
                      client=BasicMCPClient(response.strata_server_url)
                  )
              
                  # Setup LLM
                  llm = OpenAI(model="gpt-4o-mini", api_key=os.getenv("OPENAI_API_KEY"))
              
                  # Step 3: Create LlamaIndex agent with MCP tools
                  agent = FunctionAgent(
                      name="my_first_agent",
                      description="Agent using MCP-based tools",
                      tools=tools,
                      llm=llm,
                      system_prompt="You are an AI assistant that uses MCP tools.",
                  )
              
                  my_email = "<EMAIL>" # TODO: Replace with your email
                  youtube_video_url = "https://youtu.be/yebNIHKAC4A?si=1Rz_ZsiVRz0YfOR7" # TODO: Replace with your favorite youtube video URL
                  # Step 4: Invoke the agent
                  response = await agent.run(
                      f"summarize this video - {youtube_video_url} and mail this summary to my email {my_email}"
                  )
              
                  print(response)
              
              if __name__ == "__main__":
                  asyncio.run(main())
              
              ```
            </CodeGroup>
          </Tab>
          <Tab title="CrewAI">
            <Info>Coming soon</Info>
          </Tab>
          <Tab title="AutoGen">
            <CodeGroup>
                ```python Python
                import os
                import asyncio
                import webbrowser

                from dotenv import load_dotenv
                from klavis import Klavis
                from klavis.types import McpServerName
                from autogen_agentchat.agents import AssistantAgent
                from autogen_agentchat.ui import Console
                from autogen_core import CancellationToken
                from autogen_ext.models.openai import OpenAIChatCompletionClient
                from autogen_ext.tools.mcp import StreamableHttpServerParams
                from autogen_ext.tools.mcp import mcp_server_tools


                load_dotenv()

                async def main() -> None:
                    klavis_client = Klavis(api_key=os.getenv("KLAVIS_API_KEY"))

                    # Step 1: Create a Strata MCP server with Gmail and YouTube integrations
                    response = klavis_client.mcp_server.create_strata_server(
                        user_id="demo_user",
                        servers=[McpServerName.GMAIL, McpServerName.YOUTUBE],
                    )

                    # Handle OAuth authorization if required
                    if response.oauth_urls:
                        for server_name, oauth_url in response.oauth_urls.items():
                            webbrowser.open(oauth_url)
                            input(f"Press Enter after completing {server_name} OAuth authorization...")

                    server_params = StreamableHttpServerParams(
                        url=response.strata_server_url,
                        timeout=30.0,
                        sse_read_timeout=300.0,
                        terminate_on_close=True,
                    )

                    adapters = await mcp_server_tools(server_params)

                    model_client = OpenAIChatCompletionClient(model="gpt-4")
                    agent = AssistantAgent(
                        name="MultiAI",
                        model_client=model_client,
                        tools=adapters,
                        system_message="You are a helpful AI assistant.",
                    )

                    await Console(
                        agent.run_stream(
                            task="Get my latest mails.",
                            cancellation_token=CancellationToken(),
                        )
                    )
                if __name__ == "__main__":
                    asyncio.run(main())
                ```
            </CodeGroup>
          </Tab>
        </Tabs>
      </Step>
    </Steps>
  </Tab>
  <Tab title="Open Source">
    <Tip>
    Visit https://github.com/Klavis-AI/klavis to view the source code and find more information
    </Tip>
    <Steps>
      <Step title="Install Strata MCP">
        <CodeGroup>
          ```bash pipx
          pipx install strata-mcp
          ```
          ```bash pip
          pip install strata-mcp
          ```
        </CodeGroup>
      </Step>
      <Step title="Add your MCP servers">
        Configure your MCP servers using the CLI tool.
        <CodeGroup>
          ```bash Add Server
          strata add
          ```
          ```bash List Servers
          strata list
          ```
          ```bash Enable Server
          strata enable <server-name>
          ```
        </CodeGroup>
      </Step>
      <Step title="Run Strata MCP Server">
        Start the Strata server to manage all your tools.
        <CodeGroup>
          ```bash Stdio Mode (Default)
          strata
          ```
          ```bash HTTP/SSE Mode
          strata run --port 8080
          ```
        </CodeGroup>
      </Step>
      <Step title="Connect to your AI application">
        Use the Strata tool to add your AI client.
        <CodeGroup>
          ```bash Claude Code
          strata tool add claude
          ```
          ```bash Cursor
          strata tool add cursor
          ```
          ```bash VSCode
          strata tool add vscode
          ```
        </CodeGroup>
      </Step>
    </Steps>
  </Tab>
</Tabs>

## Individual-app integration

Connect a single integration (like Gmail or GitHub) as its own MCP server.

<Tabs>
  <Tab title="UI">
    <Steps>
      <Step title="Open Dashboard">
        Go to your <a href="https://www.klavis.ai/home">Dashboard</a>.
      </Step>
      <Step title="Choose apps">
        Choose an integration (for example, Gmail), and get the "individual server url"
        
        <img
          className="block dark:hidden"
          src="/images/get-started/quickstart/individual_url.png"
          alt="Individual Server URL"
        />
        <img
          className="hidden dark:block"
          src="/images/get-started/quickstart/individual_url.png"
          alt="Individual Server URL"
        />
      </Step>
      <Step title="Authenticate">
        Complete Auth by Click "Anthorize" button.
      </Step>
      <Step title="Use in your app">
        Add to your favorite MCP-supported clients, such as Cursor, Claude Code, VS Code, ChatGPT, etc.
      </Step>
    </Steps>
  </Tab>
  <Tab title="API">
    <Steps>
      <Step title="Install the SDKs (optional)">
        <CodeGroup>
          ```bash pip
          pip install klavis
          ```

          ```bash npm
          npm install klavis
          ```
        </CodeGroup>
      </Step>
      <Step title="Create a server instance">
        <CodeGroup>
          ```bash Curl
          curl -X POST "https://api.klavis.ai/mcp-server/instance/create" \
            -H "Authorization: Bearer YOUR_KLAVIS_API_KEY" \
            -H "Content-Type: application/json" \
            -d '{
              "serverName": "Gmail",
              "userId": "user123"
            }'
          ```

          ```python Python
          from klavis import Klavis
          from klavis.types import McpServerName

          klavis_client = Klavis(api_key="YOUR_KLAVIS_API_KEY")

          server = klavis_client.mcp_server.create_server_instance(
              server_name=McpServerName.GMAIL,
              user_id="user123",
          )
          print(server.server_url)
          ```

          ```javascript TypeScript
          import { KlavisClient, Klavis } from 'klavis';

          const klavisClient = new KlavisClient({ apiKey: 'YOUR_KLAVIS_API_KEY' });
          const server = await klavisClient.mcpServer.createServerInstance({
            serverName: Klavis.McpServerName.Gmail,
            userId: 'user123',
          });
          console.log(server.serverUrl);
          ```
        </CodeGroup>
        <Info>
        **Response Information**: The API returns:
        - `serverUrl`: The URL you'll use to connect your MCP client to the individual MCP Server
        - `oauthUrl`: Authorization link if the service requires OAuth authentication
        </Info>
        <Card title="API Reference" icon="magnifying-glass" href="/api-reference/mcp-server/create-a-server-instance" horizontal>
          Full Individual MCP Server endpoints
        </Card>
      </Step>
      <Step title="Authenticate">
        <CodeGroup>
          ```bash Curl
          Copy and paste the OAuth URL into your web browser
          ```
          ```python Python
          import webbrowser
          if getattr(server, 'oauth_url', None):
              webbrowser.open(server.oauth_url)
          ```

          ```javascript TypeScript
          if (server.oauthUrl) {
            window?.open?.(server.oauthUrl);
          }
          ```
        </CodeGroup>
        <Info>
        **Authentication Methods**:
        - **API Key**: See [API Key authentication guide](/documentation/auth/api-key) for details.
        - **OAuth**: See [OAuth authentication guide](/documentation/auth/oauth) for details.
        </Info>
        
        <Check>
        🎉 **Your MCP Server URL is ready to use!** Once authentication is complete, you can use your MCP server URL with any MCP-compatible client.
        </Check>
      </Step>
      <Step title="(optional) Connect to your AI application">
        <Tabs>
          <Tab title="LangChain">
            <CodeGroup>
              ```python Python
              import os
              import asyncio
              import webbrowser

              from klavis import Klavis
              from klavis.types import McpServerName
              from langchain_openai import ChatOpenAI
              from langchain_mcp_adapters.client import MultiServerMCPClient
              from langgraph.prebuilt import create_react_agent

              from dotenv import load_dotenv
              load_dotenv()

              async def main() -> None:
                  klavis_client = Klavis(api_key=os.getenv("KLAVIS_API_KEY"))

                  # Step 1: Create a single MCP server (e.g., Gmail)
                  response = klavis_client.mcp_server.create_server_instance(
                      server_name=McpServerName.GMAIL,
                      user_id="demo_user",
                  )

                  # Step 2: Handle OAuth authorization if needed
                  if hasattr(response, 'oauth_url') and response.oauth_url:
                      webbrowser.open(response.oauth_url)
                      input("Press Enter after completing OAuth authorization...")

                  # Step 3: Create LangChain Agent with MCP Tools
                  mcp_client = MultiServerMCPClient({
                      "gmail": {
                          "transport": "streamable_http",
                          "url": response.server_url,
                      }
                  })

                  # Get all available tools from the server
                  tools = await mcp_client.get_tools()
                  # Setup LLM
                  llm = ChatOpenAI(model="gpt-4o-mini", api_key=os.getenv("OPENAI_API_KEY"))
                  
                  # Step 4: Create LangChain agent with MCP tools
                  agent = create_react_agent(
                      model=llm,
                      tools=tools,
                      prompt=(
                          "You are a helpful assistant that can use MCP tools. "
                      ),
                  )

                  # Step 5: Invoke the agent
                  result = await agent.ainvoke({
                      "messages": [{"role": "user", "content": "Search my inbox for unread emails and summarize."}],
                  })
                  
                  # Print only the final AI response content
                  print(result["messages"][-1].content)

              if __name__ == "__main__":
                  asyncio.run(main())
              ```
            </CodeGroup>
          </Tab>
          <Tab title="LlamaIndex">
            <CodeGroup>
              ```python Python
              import os
              import asyncio
              import webbrowser
              
              from klavis import Klavis
              from klavis.types import McpServerName
              from llama_index.llms.openai import OpenAI
              from llama_index.core.agent.workflow import FunctionAgent
              from llama_index.tools.mcp import BasicMCPClient
              from llama_index.tools.mcp import (
                  aget_tools_from_mcp_url,
              )
              
              from dotenv import load_dotenv
              load_dotenv()
              
              async def main() -> None:
                  klavis_client = Klavis(api_key=os.getenv("KLAVIS_API_KEY"))
              
                  # Step 1: Create a single MCP server (e.g., Gmail)
                  response = klavis_client.mcp_server.create_server_instance(
                      server_name=McpServerName.GMAIL,
                      user_id="demo_user",
                  )
              
                  # Step 2: Handle OAuth authorization if needed
                  if hasattr(response, 'oauth_url') and response.oauth_url:
                      webbrowser.open(response.oauth_url)
                      input("Press Enter after completing OAuth authorization...")
              
                  # Step 3: Create LlamaIndex Agent with MCP Tools
                  tools = await aget_tools_from_mcp_url(
                      response.server_url,
                      client=BasicMCPClient(response.server_url)
                  )
              
                  # Setup LLM
                  llm = OpenAI(model="gpt-4o-mini", api_key=os.getenv("OPENAI_API_KEY"))
              
                  # Step 4: Create LlamaIndex agent with MCP tools
                  agent = FunctionAgent(
                      name="gmail_agent",
                      description="Agent using Gmail MCP tools",
                      tools=tools,
                      llm=llm,
                      system_prompt=(
                          "You are a helpful assistant that can use MCP tools. "
                      ),
                  )
              
                  # Step 5: Invoke the agent
                  result = await agent.run(
                      "Search my inbox for unread emails and summarize."
                  )
              
                  # Print the response
                  print(result)
              
              if _name_ == "_main_":
                  asyncio.run(main())

              ```
            </CodeGroup>
          </Tab>
          <Tab title="CrewAI">
            <Info>Coming soon</Info>
          </Tab>
          <Tab title="AutoGen">
            <CodeGroup>
                ```python Python
                import os
                import asyncio
                import webbrowser

                from dotenv import load_dotenv
                from klavis import Klavis
                from klavis.types import McpServerName
                from autogen_agentchat.agents import AssistantAgent
                from autogen_agentchat.ui import Console
                from autogen_core import CancellationToken
                from autogen_ext.models.openai import OpenAIChatCompletionClient
                from autogen_ext.tools.mcp import StreamableHttpMcpToolAdapter, StreamableHttpServerParams
                from autogen_ext.tools.mcp import mcp_server_tools


                load_dotenv()

                async def main() -> None:
                    klavis_client = Klavis(api_key=os.getenv("KLAVIS_API_KEY"))

                    # Create MCP server instance
                    response = klavis_client.mcp_server.create_server_instance(
                        server_name=McpServerName.GMAIL,
                        user_id="demo_user",
                    )

                    # Handle OAuth authorization if required
                    if getattr(response, "oauth_url", None):
                        webbrowser.open(response.oauth_url)
                        input("Press Enter after completing OAuth authorization...")

                    server_params = StreamableHttpServerParams(
                        url=response.server_url,
                        timeout=30.0,
                        sse_read_timeout=300.0,
                        terminate_on_close=True,
                    )

                    adapters = await mcp_server_tools(server_params)

                    model_client = OpenAIChatCompletionClient(model="gpt-4")
                    agent = AssistantAgent(
                        name="MailAI",
                        model_client=model_client,
                        tools=adapters,
                        system_message="You are a helpful Gmail assistant.",
                    )

                    await Console(
                        agent.run_stream(
                            task="Find My Latest Emails",
                            cancellation_token=CancellationToken()
                        )
                    )

                if __name__ == "__main__":
                    asyncio.run(main())
                ```
            </CodeGroup>
          </Tab>
        </Tabs>
      </Step>
    </Steps>
  </Tab>
  <Tab title="Open Source">
    <Tip>
    Visit https://github.com/Klavis-AI/klavis/mcp_servers to view the source code and find more information
    </Tip>
    <Steps>
      <Step title="Run the server locally or in your infra">
        <CodeGroup>
          ```bash Docker
          docker run -p 5000:5000 ghcr.io/klavis-ai/gmail-mcp-server:latest
          ```
        </CodeGroup>
        <Info>
        Browse all available MCP server Docker images at [GitHub Packages](https://github.com/orgs/Klavis-AI/packages?repo_name=klavis)
        </Info>
      </Step>
      <Step title="Point your MCP client to the URL">
        Use the local URL (for example, http://localhost:5000) in your client or aggregator.
      </Step>
      <Step title="Secure and deploy">
        Add TLS, auth, and deploy behind your gateway as needed.
      </Step>
    </Steps>
  </Tab>
</Tabs>

## Next steps

<CardGroup cols={2}>
  <Card title="AI Platform Integrations" icon="handshake" href="/documentation/ai-platform-integration/overview">
    Integrate Klavis MCP Servers with leading AI platforms
  </Card>
  <Card title="Integrations" icon="server" href="/documentation/mcp-server/github">
    Explore available MCP servers
  </Card>
  <Card title="Strata" icon="layer-group" href="/documentation/concepts/strata">
    Progressive tool discovery across apps
  </Card>
  <Card title="API Reference" icon="magnifying-glass" href="/api-reference/introduction">
    REST endpoints and schemas
  </Card>
</CardGroup>
